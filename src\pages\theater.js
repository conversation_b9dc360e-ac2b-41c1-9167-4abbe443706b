const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Day.js for better time handling
const dayjs = require('dayjs');

// Global state
let activeTab = "tickets";
let currentUser = null;
let userPermissions = [];

// Current user management
async function loadCurrentUser() {
    try {
        console.log('Theater - Loading current user...');
        currentUser = await ipcRenderer.invoke('get-current-user');

        if (currentUser) {
            userPermissions = currentUser.permissions || [];
            console.log('Theater - User loaded:', {
                username: currentUser.username,
                role: currentUser.role,
                permissionCount: userPermissions.length
            });

            updateOperatorInfo();
            updateExitButton(); // Update button based on permissions
        } else {
            console.error('Theater - No current user returned');
            document.getElementById('current-operator').textContent = 'Error: No User';
        }
    } catch (error) {
        console.error('Error loading current user:', error);
        document.getElementById('current-operator').textContent = 'Error: User Load Failed';
    }
}

function updateOperatorInfo() {
    if (currentUser) {
        const operatorSpan = document.getElementById('current-operator');
        if (operatorSpan) {
            operatorSpan.textContent = currentUser.name || currentUser.username || 'Unknown';
        }

        // Update location name in header
        const locationNameElement = document.getElementById('location-name');
        if (locationNameElement) {
            if (currentUser.location_name) {
                locationNameElement.textContent = currentUser.location_name;
            } else {
                locationNameElement.textContent = 'Rainbow Station Inc.';
            }
        }
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    updateTime();
    setInterval(updateTime, 1000);
    renderContent();

    // Load current user information
    await loadCurrentUser();

    // Load duration if tickets tab is the default active tab
    if (activeTab === 'tickets') {
        setTimeout(async () => {
            await loadLocationDuration();
        }, 200); // Delay to ensure DOM is fully rendered
    }

    // Initialize maximize button state
    try {
        const isMaximized = await ipcRenderer.invoke('is-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error checking initial maximize state:', error);
    }

    // Listen for system refresh requests from main process
    ipcRenderer.on('system-refresh-requested', async () => {
        console.log('🔄 Theater - System refresh requested by main process');
        await refreshTheaterData();
        showTheaterNotification('Theater data refreshed successfully!', 'success');
    });

    // Listen for location updates from main process
    ipcRenderer.on('location-updated', async (event, updateData) => {
        console.log('🏢 Theater - Location updated:', updateData);

        // If user data was already refreshed in main process, just reload current user
        // Otherwise, force refresh from database
        if (updateData.userRefreshed) {
            console.log('🔄 Theater - User data already refreshed in main process');
            await loadCurrentUser();
        } else {
            console.log('🔄 Theater - Requesting user data refresh...');
            const refreshResult = await ipcRenderer.invoke('refresh-current-user');
            if (refreshResult.success) {
                await loadCurrentUser();
            }
        }

        // If we're on the tickets tab, reload the duration
        if (activeTab === 'tickets') {
            await loadLocationDuration();
        }

        showTheaterNotification('Location settings updated!', 'info');
    });

    // Listen for theater time updates specifically
    ipcRenderer.on('theater-time-updated', async (event, updateData) => {
        console.log('🎬 Theater - Theater time updated:', updateData);

        // If user data was already refreshed in main process, just reload current user
        // Otherwise, force refresh from database
        if (updateData.userRefreshed) {
            console.log('🔄 Theater - User data already refreshed in main process');
            await loadCurrentUser();
        } else {
            console.log('🔄 Theater - Requesting user data refresh for theater time...');
            const refreshResult = await ipcRenderer.invoke('refresh-current-user');
            if (refreshResult.success) {
                await loadCurrentUser();
            }
        }

        // If we're on the tickets tab, reload the duration immediately
        if (activeTab === 'tickets') {
            await loadLocationDuration();
            showTheaterNotification(`Theater duration updated to ${updateData.theaterTime} minutes!`, 'success');
        } else {
            showTheaterNotification('Theater duration updated!', 'success');
        }
    });
});

// Theater data refresh function
async function refreshTheaterData() {
    try {
        console.log('🔄 Theater - Refreshing all theater data...');

        // Force refresh current user data from database to get latest location settings
        console.log('🔄 Theater - Requesting fresh user data from database...');
        const refreshResult = await ipcRenderer.invoke('refresh-current-user');
        if (refreshResult.success) {
            await loadCurrentUser();
            console.log('✅ Theater - User data refreshed from database');
        } else {
            console.warn('⚠️ Theater - Failed to refresh user data, using cached data');
            await loadCurrentUser();
        }

        // Refresh data based on current active tab
        switch (activeTab) {
            case 'tickets':
                // Force reload location duration with fresh data
                await loadLocationDuration(true);
                console.log('🔄 Theater - Tickets tab data refreshed');
                break;

            case 'current':
                // Reload current tickets list
                await loadTicketList(true);
                console.log('🔄 Theater - Current viewers data refreshed');
                break;

            case 'banned':
                // Reload banned tickets list
                await loadBannedTicketList();
                console.log('🔄 Theater - Banned tickets data refreshed');
                break;

            default:
                console.log('🔄 Theater - No specific refresh for tab:', activeTab);
        }

        console.log('✅ Theater - All data refreshed successfully');

    } catch (error) {
        console.error('❌ Theater - Error refreshing data:', error);
        throw error;
    }
}

// Theater notification system
function showTheaterNotification(message, type = 'info') {
    // Remove any existing notifications
    const existingNotifications = document.querySelectorAll('.theater-notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'theater-notification';
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        max-width: 400px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        font-size: 14px;
    `;

    // Set background color based on type
    switch (type) {
        case 'success':
            notification.style.backgroundColor = '#10b981';
            break;
        case 'error':
            notification.style.backgroundColor = '#ef4444';
            break;
        case 'warning':
            notification.style.backgroundColor = '#f59e0b';
            break;
        default:
            notification.style.backgroundColor = '#3b82f6';
    }

    notification.textContent = message;
    document.body.appendChild(notification);

    // Auto-remove after 4 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => notification.remove(), 300);
        }
    }, 4000);
}

// Theater system refresh button handler
async function refreshTheaterSystem() {
    const refreshBtn = document.getElementById('tab-refresh');

    if (!refreshBtn) {
        console.error('Refresh button not found');
        return;
    }

    // Show loading state
    const originalText = refreshBtn.innerHTML;
    const originalClass = refreshBtn.className;

    refreshBtn.innerHTML = `
        <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
        </svg>
        REFRESHING...
    `;
    refreshBtn.className = 'tab-btn refresh loading';
    refreshBtn.disabled = true;

    try {
        console.log('🔄 Theater - Manual refresh initiated...');

        // Call the refresh function
        await refreshTheaterData();

        // Show success state
        refreshBtn.innerHTML = `
            <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>
            REFRESHED!
        `;
        refreshBtn.className = 'tab-btn refresh success';

        showTheaterNotification('Theater system refreshed successfully!', 'success');

        // Restore original state after 2 seconds
        setTimeout(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.className = originalClass;
            refreshBtn.disabled = false;
        }, 2000);

    } catch (error) {
        console.error('❌ Theater - Manual refresh failed:', error);

        // Show error state
        refreshBtn.innerHTML = `
            <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                <line x1="18" y1="6" x2="6" y2="18"/>
                <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
            FAILED
        `;
        refreshBtn.className = 'tab-btn refresh error';

        showTheaterNotification(`Refresh failed: ${error.message}`, 'error');

        // Restore original state after 3 seconds
        setTimeout(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.className = originalClass;
            refreshBtn.disabled = false;
        }, 3000);
    }
}

// Time functions
function updateTime() {
    const now = new Date();
    document.getElementById('current-date').textContent = formatDate(now);
    document.getElementById('current-time').textContent = formatTime(now);
}

function formatDate(date) {
    return date.toLocaleDateString("en-US", { 
        year: "numeric", 
        month: "2-digit", 
        day: "2-digit" 
    });
}

function formatTime(date) {
    return date.toLocaleTimeString("en-US", { hour12: false });
}

// Permission checking functions
function hasModuleAccess(moduleId) {
    // Admin has access to everything
    if (currentUser && currentUser.role === 'Admin') {
        return true;
    }

    // Check if user has permission for this module
    return userPermissions && userPermissions.some(perm => perm.module_id === moduleId);
}

function hasPOSAccess() {
    return hasModuleAccess('pos');
}

// Update exit button based on user permissions
function updateExitButton() {
    const exitButton = document.querySelector('.back-btn');

    if (!exitButton) {
        console.error('Theater - Exit button not found');
        return;
    }

    if (hasPOSAccess()) {
        // User has POS access - show "Back to POS" button
        console.log('Theater - User has POS access, showing Back to POS button');
        exitButton.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="19" y1="12" x2="5" y2="12"/>
                <polyline points="12,19 5,12 12,5"/>
            </svg>
            Back to POS
        `;
        exitButton.title = 'Return to Point of Sale System';
    } else {
        // User has NO POS access - show "LOGOUT" button
        console.log('Theater - User has NO POS access, showing Logout button');
        exitButton.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                <polyline points="16,17 21,12 16,7"/>
                <line x1="21" y1="12" x2="9" y2="12"/>
            </svg>
            LOGOUT
        `;
        exitButton.title = 'Logout from Theater Management System';
    }
}

// Navigation functions
function handleExit() {
    if (hasPOSAccess()) {
        // User has POS access - navigate to POS
        console.log('Theater - Navigating back to POS');
        ipcRenderer.invoke('navigate-to', 'pos');
    } else {
        // User has NO POS access - logout
        console.log('Theater - User has no POS access, logging out');
        handleLogout();
    }
}

async function handleLogout() {
    try {
        console.log('Theater - Logging out...');
        const result = await ipcRenderer.invoke('logout');
        if (result.success) {
            console.log('Theater - Logout successful');
        } else {
            console.error('Theater - Logout failed:', result.message);
        }
    } catch (error) {
        console.error('Theater - Logout error:', error);
    }
}

// Window control functions
function closeApp() {
    ipcRenderer.invoke('close-app');
}

function minimizeApp() {
    ipcRenderer.invoke('minimize-app');
}

async function toggleFullscreen() {
    try {
        const isMaximized = await ipcRenderer.invoke('toggle-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error toggling maximize:', error);
    }
}

function setActiveTab(tabId) {
    activeTab = tabId;

    // Update tab visual states
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    const activeTabBtn = document.getElementById(`tab-${tabId}`);
    if (activeTabBtn && !activeTabBtn.classList.contains('exit')) {
        activeTabBtn.classList.add('active');
    }

    renderContent();

    // Load duration if tickets tab is activated
    if (tabId === 'tickets') {
        setTimeout(async () => {
            await loadLocationDuration();
        }, 100); // Small delay to ensure DOM is updated
    }

    // Load ticket list if current viewer tab is activated
    if (tabId === 'current') {
        setTimeout(() => {
            loadTicketList();
            startTicketStatusUpdater(); // Start real-time updates
        }, 100); // Small delay to ensure DOM is updated
    } else {
        // Stop real-time updates when switching away from current viewer tab
        stopTicketStatusUpdater();
    }

    // Load banned tickets if banned tab is activated
    if (tabId === 'banned') {
        setTimeout(() => {
            loadBannedTicketList();
        }, 100); // Small delay to ensure DOM is updated
    }
}

// Content rendering functions
function renderContent() {
    const contentArea = document.getElementById('content-area');
    
    switch (activeTab) {
        case "tickets":
            contentArea.innerHTML = renderTicketsContent();
            break;
        case "current":
            contentArea.innerHTML = renderCurrentViewersContent();
            break;
        case "banned":
            contentArea.innerHTML = renderBannedViewersContent();
            break;
        default:
            contentArea.innerHTML = renderTicketsContent();
    }
}

function renderTicketsContent() {
    return `
        <div class="ticket-issue-fullwidth-container">
            <!-- Left Side: Controls -->
            <div class="ticket-controls-panel">
                <div class="panel-header">
                    <h2 class="panel-title">
                        <svg class="title-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2 6a2 2 0 012-2h16a2 2 0 012 2v2H2V6zM2 10h20v8a2 2 0 01-2 2H4a2 2 0 01-2-2v-8z"/>
                        </svg>
                        Issue New Ticket
                    </h2>
                    <p class="panel-subtitle">Configure ticket settings and capture visitor photo</p>
                </div>

                <div class="controls-content"">

                <!-- Duration Section -->
                <div class="big-form-section">
                    <label class="big-form-label">
                        <svg class="label-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm4.2 14.2L11 13V7h1.5v5.2l4.5 2.7-.8 1.3z"/>
                        </svg>
                        Duration (Minutes)
                    </label>
                    <div class="duration-input-container">
                        <input type="number" id="duration-input" class="big-duration-input" value="120" min="1" max="999" placeholder="Loading..." readonly>
                        <span class="duration-unit-big">minutes</span>
                    </div>
                    <p class="input-help-text">Duration is automatically set based on your location settings</p>
                </div>

                <!-- Payment Method Section -->
                <div class="big-form-section">
                    <label class="big-form-label">
                        <svg class="label-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"/>
                        </svg>
                        Payment Method
                    </label>
                    <div class="big-payment-methods">
                        <button class="big-payment-btn" onclick="selectPaymentMethod('cash')" data-method="cash">
                            <svg class="big-payment-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M7 15h2c0 1.08 1.37 2 3 2s3-.92 3-2c0-1.1-1.04-1.5-3.24-2.03C9.64 12.44 7 11.78 7 9c0-1.79 1.47-3.31 3.5-3.82V3h3v2.18C15.53 5.69 17 7.21 17 9h-2c0-1.08-1.37-2-3-2s-3 .92-3 2c0 1.1 1.04 1.5 3.24 2.03C14.36 11.56 17 12.22 17 15c0 1.79-1.47 3.31-3.5 3.82V21h-3v-2.18C8.47 18.31 7 16.79 7 15z"/>
                            </svg>
                            <span class="payment-text">CASH</span>
                        </button>
                        <button class="big-payment-btn" onclick="selectPaymentMethod('credit')" data-method="credit">
                            <svg class="big-payment-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"/>
                            </svg>
                            <span class="payment-text">CREDIT</span>
                        </button>
                        <button class="big-payment-btn" onclick="selectPaymentMethod('debit')" data-method="debit">
                            <svg class="big-payment-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"/>
                            </svg>
                            <span class="payment-text">DEBIT</span>
                        </button>
                    </div>
                    <p class="input-help-text">Select the payment method used by the visitor</p>
                </div>

                <!-- Action Buttons -->
                <div class="big-form-section">
                    <div class="action-buttons">
                        <button class="big-capture-btn" onclick="capturePhoto()">
                            <svg class="btn-icon-big" viewBox="0 0 24 24" fill="currentColor">
                                <circle cx="12" cy="12" r="3"/>
                                <path d="M12 1a9 9 0 0 0-9 9v3a9 9 0 0 0 18 0V10a9 9 0 0 0-9-9zM12 8a4 4 0 1 1 0 8 4 4 0 0 1 0-8z"/>
                            </svg>
                            CAPTURE PHOTO
                        </button>
                        <button class="big-clear-btn" onclick="clearPhoto()">
                            <svg class="btn-icon-big" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                            </svg>
                            CLEAR PHOTO
                        </button>
                    </div>
                </div>

                <!-- Issue Ticket and Reset Buttons -->
                <div class="big-form-section">
                    <div class="main-action-buttons">
                        <button class="big-issue-ticket-btn" onclick="issueTicket()">
                            <svg class="btn-icon-big" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M2 6a2 2 0 012-2h16a2 2 0 012 2v2H2V6zM2 10h20v8a2 2 0 01-2 2H4a2 2 0 01-2-2v-8z"/>
                            </svg>
                            ISSUE TICKET
                        </button>
                        <button class="big-reset-btn" onclick="manualResetForm()">
                            <svg class="btn-icon-big" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                            </svg>
                            RESET FORM
                        </button>
                    </div>
                </div>

                </div> <!-- End controls-content -->
            </div>

            <!-- Right Side: Image Preview -->
            <div class="ticket-preview-panel">
                <div class="panel-header">
                    <h2 class="panel-title">
                        <svg class="title-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5a3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97c0-.33-.03-.65-.07-.97l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.32-.07.65-.07.97c0 .33.03.65.07.97L2.46 14.6c-.19.15-.24.42-.12.64l2 3.46c.12.22.39.31.61.22l2.49-1c.52.39 1.06.73 1.69.98l.37 2.65c.***********.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.25 1.17-.59 1.69-.98l2.49 1c.22.09.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.63Z"/>
                        </svg>
                        Visitor Photo
                    </h2>
                    <p class="panel-subtitle">Captured photo will appear here</p>
                </div>

                <div class="big-photo-container">
                    <div class="big-photo-preview" id="photo-preview">
                        <div class="big-photo-placeholder">
                            <div class="photo-placeholder-content">
                                <svg class="big-camera-icon" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5a3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97c0-.33-.03-.65-.07-.97l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.32-.07.65-.07.97c0 .33.03.65.07.97L2.46 14.6c-.19.15-.24.42-.12.64l2 3.46c.12.22.39.31.61.22l2.49-1c.52.39 1.06.73 1.69.98l.37 2.65c.***********.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.25 1.17-.59 1.69-.98l2.49 1c.22.09.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.63Z"/>
                                </svg>
                                <h3 class="big-photo-text">No Photo Captured</h3>
                                <p class="big-photo-subtext">Click <strong>"CAPTURE PHOTO"</strong> button to take a visitor photo</p>
                                <div class="photo-help-tips">
                                    <p class="photo-tip">📸 High quality photos supported</p>
                                    <p class="photo-tip">🔄 Multiple tickets with same photo allowed</p>
                                </div>
                            </div>
                        </div>
                        <img id="captured-photo" class="big-captured-photo" style="display: none;" alt="Captured photo">
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderCurrentViewersContent() {
    return `
        <div class="ticket-list-container">
            <div class="ticket-list-header">
                <h2 class="ticket-list-title">
                    <svg class="title-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Current Viewer List
                </h2>
            </div>

            <div class="ticket-controls">
                <button class="refresh-btn" onclick="loadTicketList()">
                    <svg class="btn-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                    </svg>
                    REFRESH LIST
                </button>
                <div class="filter-controls">
                    <select class="filter-select" id="status-filter" onchange="filterTickets()">
                        <option value="all">All Tickets</option>
                        <option value="active">Active Only</option>
                        <option value="expired">Expired Only</option>
                    </select>
                </div>
            </div>

            <div class="tickets-grid" id="tickets-grid">
                <div class="loading-placeholder">
                    <div class="loading-spinner"></div>
                    <p>Loading tickets...</p>
                </div>
            </div>
        </div>
    `;
}

function renderBannedViewersContent() {
    return `
        <div>
            <h2 class="content-title">Banned Viewer List</h2>
            <p class="content-description">Manage banned viewers and access restrictions</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 24px; margin-bottom: 32px;">
                <div style="background: linear-gradient(135deg, #ef4444, #dc2626); color: #ffffff; border-radius: 16px; padding: 28px; box-shadow: 0 12px 24px -8px rgba(239, 68, 68, 0.4); position: relative; overflow: hidden;">
                    <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
                    <div style="position: relative; z-index: 1;">
                        <h3 style="font-size: 16px; font-weight: 700; margin-bottom: 12px; text-transform: uppercase; letter-spacing: 0.5px;">🚫 Total Banned</h3>
                        <p style="font-size: 42px; font-weight: 900; margin-bottom: 8px; line-height: 1;" id="banned-count">0</p>
                        <p style="opacity: 0.9; font-weight: 500;">Access denied</p>
                    </div>
                </div>
                <div style="background: linear-gradient(135deg, #10b981, #059669); color: #ffffff; border-radius: 16px; padding: 28px; box-shadow: 0 12px 24px -8px rgba(16, 185, 129, 0.4); position: relative; overflow: hidden;">
                    <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
                    <div style="position: relative; z-index: 1;">
                        <h3 style="font-size: 16px; font-weight: 700; margin-bottom: 12px; text-transform: uppercase; letter-spacing: 0.5px;">💰 Refunded</h3>
                        <p style="font-size: 42px; font-weight: 900; margin-bottom: 8px; line-height: 1;" id="refunded-count">0</p>
                        <p style="opacity: 0.9; font-weight: 500;">Money returned</p>
                    </div>
                </div>
                <div style="background: linear-gradient(135deg, #f59e0b, #d97706); color: #ffffff; border-radius: 16px; padding: 28px; box-shadow: 0 12px 24px -8px rgba(245, 158, 11, 0.4); position: relative; overflow: hidden;">
                    <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
                    <div style="position: relative; z-index: 1;">
                        <h3 style="font-size: 16px; font-weight: 700; margin-bottom: 12px; text-transform: uppercase; letter-spacing: 0.5px;">⏳ Pending</h3>
                        <p style="font-size: 42px; font-weight: 900; margin-bottom: 8px; line-height: 1;" id="pending-count">0</p>
                        <p style="opacity: 0.9; font-weight: 500;">Not refunded</p>
                    </div>
                </div>
            </div>

            <div style="background-color: #f9fafb; border: 2px solid #e5e7eb; border-radius: 12px; padding: 32px;">
                <h3 style="font-size: 24px; font-weight: bold; color: #1f2937; margin-bottom: 24px; text-align: center;">🚫 Banned Tickets</h3>

                <div id="banned-tickets-container">
                    <div class="loading-message" style="text-align: center; padding: 40px; color: #6b7280;">
                        <div style="display: inline-block; width: 32px; height: 32px; border: 3px solid #e5e7eb; border-top: 3px solid #3b82f6; border-radius: 50%; animation: spin 1s linear infinite; margin-bottom: 16px;"></div>
                        <p style="margin: 0; font-weight: 600;">Loading banned tickets...</p>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Ticket Issue Form Functions
let selectedPaymentMethod = null;
let capturedPhotoData = null;

// Load duration from current user's location
async function loadLocationDuration(forceRefresh = false) {
    const durationInput = document.getElementById('duration-input');
    if (!durationInput) {
        console.log('Duration input not found, tab may not be active yet');
        return;
    }

    // If forceRefresh is true, force a database refresh of user data
    if (forceRefresh) {
        console.log('🔄 Theater - Force refreshing user data from database...');
        const refreshResult = await ipcRenderer.invoke('refresh-current-user');
        if (refreshResult.success) {
            await loadCurrentUser();
            console.log('✅ Theater - User data refreshed from database');
        } else {
            console.warn('⚠️ Theater - Failed to refresh user data, using cached data');
            await loadCurrentUser();
        }
    } else if (!currentUser || !currentUser.theater_time) {
        // If no theater_time, just reload current user from main process cache
        console.log('🔄 Theater - Reloading user data for theater time...');
        await loadCurrentUser();
    }

    if (currentUser && currentUser.theater_time) {
        // Parse the theater_time value (could be in various formats)
        let duration = parseInt(currentUser.theater_time);

        // Validate duration
        if (isNaN(duration) || duration <= 0) {
            console.warn('Invalid theater_time value:', currentUser.theater_time);
            duration = 120; // Default fallback
        }

        durationInput.value = duration;
        durationInput.placeholder = `${duration} minutes (from location settings)`;

        console.log(`✅ Duration loaded from location: ${duration} minutes`);
    } else {
        // No location or theater_time set, use default
        const defaultDuration = 120;
        durationInput.value = defaultDuration;
        durationInput.placeholder = `${defaultDuration} minutes (default)`;

        console.log('⚠️ No location duration found, using default:', defaultDuration);
    }
}

function capturePhoto() {
    // Open camera modal
    openCameraModal();
}

function clearPhoto() {
    const photoPreview = document.getElementById('photo-preview');
    const placeholder = photoPreview.querySelector('.big-photo-placeholder');
    const capturedPhoto = document.getElementById('captured-photo');

    if (placeholder) placeholder.style.display = 'flex';
    capturedPhoto.style.display = 'none';
    capturedPhoto.src = '';
    capturedPhotoData = null;

    // Also clear camera modal if open
    const canvas = document.getElementById('captured-canvas');
    const capturePlaceholder = document.getElementById('capture-placeholder');
    if (canvas && capturePlaceholder) {
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        canvas.style.display = 'none';
        capturePlaceholder.style.display = 'block';

        const removeBtn = document.getElementById('remove-all-btn');
        if (removeBtn) removeBtn.disabled = true;
    }

    console.log('Photo cleared');
}

// Camera Modal Functions
let currentStream = null;
let availableCameras = [];

async function openCameraModal() {
    const modal = document.getElementById('camera-modal');
    modal.style.display = 'flex';

    // Add click outside to close functionality
    modal.addEventListener('click', function(event) {
        if (event.target === modal) {
            closeCameraModal();
        }
    });

    // Load available cameras
    await loadAvailableCameras();
}

function closeCameraModal() {
    const modal = document.getElementById('camera-modal');
    modal.style.display = 'none';

    // Stop current camera stream
    if (currentStream) {
        currentStream.getTracks().forEach(track => track.stop());
        currentStream = null;
    }

    // Reset UI
    resetCameraUI();
}

async function loadAvailableCameras() {
    try {
        // First request permission to access cameras
        await navigator.mediaDevices.getUserMedia({ video: true })
            .then(stream => {
                // Stop the stream immediately, we just needed permission
                stream.getTracks().forEach(track => track.stop());
            })
            .catch(error => {
                console.warn('Camera permission not granted:', error);
            });

        // Now enumerate all available devices
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(device => device.kind === 'videoinput');

        const select = document.getElementById('webcam-source-select');
        select.innerHTML = '<option value="">Select Camera...</option>';

        availableCameras = videoDevices;

        if (videoDevices.length === 0) {
            const option = document.createElement('option');
            option.value = "";
            option.textContent = "No cameras found - Please connect a USB camera";
            option.disabled = true;
            select.appendChild(option);
            console.log('No cameras detected. Please connect a USB camera.');
            return;
        }

        videoDevices.forEach((device, index) => {
            const option = document.createElement('option');
            option.value = device.deviceId;

            // Better labeling for USB cameras
            let label = device.label;
            if (!label || label.trim() === '') {
                label = `Camera ${index + 1}`;
            }

            // Add additional info for USB cameras
            if (label.toLowerCase().includes('usb') ||
                label.toLowerCase().includes('external') ||
                label.toLowerCase().includes('webcam')) {
                label += ' (USB Camera)';
            }

            option.textContent = label;
            select.appendChild(option);
        });

        // Add event listener for camera selection (remove existing first)
        select.removeEventListener('change', handleCameraSelection);
        select.addEventListener('change', handleCameraSelection);

        console.log(`Found ${videoDevices.length} camera(s):`);
        videoDevices.forEach((device, index) => {
            console.log(`  ${index + 1}. ${device.label || 'Unknown Camera'} (ID: ${device.deviceId})`);
        });

        // Auto-select first USB camera if available
        const usbCamera = videoDevices.find(device =>
            device.label && (
                device.label.toLowerCase().includes('usb') ||
                device.label.toLowerCase().includes('external') ||
                device.label.toLowerCase().includes('webcam')
            )
        );

        if (usbCamera) {
            select.value = usbCamera.deviceId;
            await handleCameraSelection({ target: { value: usbCamera.deviceId } });
            console.log('Auto-selected USB camera:', usbCamera.label);
        }

    } catch (error) {
        console.error('Error loading cameras:', error);
        alert('Error accessing cameras. Please check that:\n1. A USB camera is connected\n2. Camera permissions are granted\n3. No other application is using the camera');
    }
}

async function handleCameraSelection(event) {
    const deviceId = event.target.value;

    if (!deviceId) {
        stopCamera();
        return;
    }

    try {
        // Stop current stream if exists
        if (currentStream) {
            currentStream.getTracks().forEach(track => track.stop());
            currentStream = null;
        }

        // Enhanced constraints for better USB camera support
        const constraints = {
            video: {
                deviceId: { exact: deviceId },
                width: { ideal: 1280, min: 640 },
                height: { ideal: 720, min: 480 },
                frameRate: { ideal: 30, min: 15 },
                facingMode: 'environment' // Prefer rear camera if available
            }
        };

        console.log('Starting camera with constraints:', constraints);

        currentStream = await navigator.mediaDevices.getUserMedia(constraints);

        const video = document.getElementById('camera-video');
        const placeholder = document.getElementById('camera-placeholder');

        video.srcObject = currentStream;

        // Wait for video to load metadata
        await new Promise((resolve) => {
            video.addEventListener('loadedmetadata', resolve, { once: true });
        });

        video.style.display = 'block';
        placeholder.style.display = 'none';

        // Enable capture button
        document.getElementById('capture-image-btn').disabled = false;

        // Get actual video dimensions and update status
        const track = currentStream.getVideoTracks()[0];
        const settings = track.getSettings();

        // Update camera status display
        updateCameraStatus('Connected', `${settings.width}x${settings.height} @ ${settings.frameRate}fps - ${track.label}`);

        console.log('Camera started successfully');
        console.log('Video dimensions:', settings.width + 'x' + settings.height);
        console.log('Frame rate:', settings.frameRate);
        console.log('Device label:', track.label);

    } catch (error) {
        console.error('Error starting camera:', error);

        // Try with fallback constraints if high quality fails
        try {
            console.log('Trying fallback constraints...');
            const fallbackConstraints = {
                video: {
                    deviceId: { exact: deviceId },
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                }
            };

            currentStream = await navigator.mediaDevices.getUserMedia(fallbackConstraints);

            const video = document.getElementById('camera-video');
            const placeholder = document.getElementById('camera-placeholder');

            video.srcObject = currentStream;
            video.style.display = 'block';
            placeholder.style.display = 'none';

            document.getElementById('capture-image-btn').disabled = false;

            console.log('Camera started with fallback constraints');

        } catch (fallbackError) {
            console.error('Fallback camera start failed:', fallbackError);
            alert('Error starting camera. Please check that:\n1. The USB camera is properly connected\n2. No other application is using the camera\n3. Camera drivers are installed correctly');
        }
    }
}

function stopCamera() {
    if (currentStream) {
        currentStream.getTracks().forEach(track => track.stop());
        currentStream = null;
    }

    const video = document.getElementById('camera-video');
    const placeholder = document.getElementById('camera-placeholder');

    video.style.display = 'none';
    placeholder.style.display = 'block';

    // Disable capture button
    document.getElementById('capture-image-btn').disabled = true;

    // Update status
    updateCameraStatus('Disconnected', 'Select a camera to start preview');
}

function captureImageFromCamera() {
    const video = document.getElementById('camera-video');
    const canvas = document.getElementById('captured-canvas');
    const capturePlaceholder = document.getElementById('capture-placeholder');

    if (!video.srcObject) {
        alert('No camera stream available');
        return;
    }

    if (video.videoWidth === 0 || video.videoHeight === 0) {
        alert('Camera is not ready. Please wait for the video to load.');
        return;
    }

    // Set canvas dimensions to match video (maintain aspect ratio)
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw video frame to canvas with high quality
    const ctx = canvas.getContext('2d');
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Show captured image
    canvas.style.display = 'block';
    capturePlaceholder.style.display = 'none';

    // Enable remove button
    document.getElementById('remove-all-btn').disabled = false;

    // Store captured image data with high quality (0.9 quality for USB cameras)
    capturedPhotoData = canvas.toDataURL('image/jpeg', 0.9);

    // Update photo display in main form
    updatePhotoDisplay();

    // Log capture details
    const track = currentStream.getVideoTracks()[0];
    const settings = track.getSettings();
    console.log('Image captured successfully');
    console.log('Capture resolution:', canvas.width + 'x' + canvas.height);
    console.log('Camera settings:', settings);
    console.log('Image size:', Math.round(capturedPhotoData.length / 1024) + ' KB');
}

function updatePhotoDisplay() {
    const photoPreview = document.getElementById('photo-preview');
    const placeholder = photoPreview.querySelector('.big-photo-placeholder');
    const capturedPhoto = document.getElementById('captured-photo');

    if (capturedPhotoData) {
        if (placeholder) placeholder.style.display = 'none';
        capturedPhoto.src = capturedPhotoData;
        capturedPhoto.style.display = 'block';
    } else {
        if (placeholder) placeholder.style.display = 'flex';
        capturedPhoto.style.display = 'none';
        capturedPhoto.src = '';
    }
}

function removeAllImages() {
    const canvas = document.getElementById('captured-canvas');
    const capturePlaceholder = document.getElementById('capture-placeholder');

    // Clear canvas
    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Hide canvas and show placeholder
    canvas.style.display = 'none';
    capturePlaceholder.style.display = 'block';

    // Disable remove button
    document.getElementById('remove-all-btn').disabled = true;

    // Clear captured photo data
    capturedPhotoData = null;

    // Update photo display in main form
    updatePhotoDisplay();

    console.log('All images removed');
}

function resetCameraUI() {
    // Reset video
    const video = document.getElementById('camera-video');
    const cameraPlaceholder = document.getElementById('camera-placeholder');
    video.style.display = 'none';
    cameraPlaceholder.style.display = 'block';

    // Reset canvas
    const canvas = document.getElementById('captured-canvas');
    const capturePlaceholder = document.getElementById('capture-placeholder');
    canvas.style.display = 'none';
    capturePlaceholder.style.display = 'block';

    // Reset buttons
    document.getElementById('capture-image-btn').disabled = true;
    document.getElementById('remove-all-btn').disabled = true;

    // Reset select
    document.getElementById('webcam-source-select').value = '';
}

// Camera status update function
function updateCameraStatus(status, details = '') {
    const statusDiv = document.getElementById('camera-status');
    const statusText = document.getElementById('camera-status-text');
    const detailsText = document.getElementById('camera-details');

    if (statusDiv && statusText) {
        statusDiv.style.display = 'block';
        statusText.textContent = status;

        if (detailsText && details) {
            detailsText.textContent = details;
        }
    }
}

// Refresh cameras function
async function refreshCameras() {
    console.log('Refreshing camera list...');
    updateCameraStatus('Refreshing camera list...', 'Please wait...');

    // Stop current camera if running
    if (currentStream) {
        currentStream.getTracks().forEach(track => track.stop());
        currentStream = null;
    }

    // Reset UI
    resetCameraUI();

    // Reload cameras
    await loadAvailableCameras();

    updateCameraStatus('Ready', 'Select a camera from the dropdown');
    console.log('Camera list refreshed');
}

function selectPaymentMethod(method) {
    // Remove previous selection from both old and new button classes
    document.querySelectorAll('.payment-btn, .big-payment-btn').forEach(btn => {
        btn.classList.remove('selected');
    });

    // Add selection to clicked button
    const selectedBtn = document.querySelector(`[data-method="${method}"]`);
    if (selectedBtn) {
        selectedBtn.classList.add('selected');
        selectedPaymentMethod = method;
        console.log('Payment method selected:', method);
    }
}

async function issueTicket() {
    const duration = document.getElementById('duration-input').value;

    // Validate form
    if (!duration || duration < 1) {
        alert('Please enter a valid duration');
        return;
    }

    if (!capturedPhotoData) {
        alert('Please capture a photo before issuing the ticket');
        return;
    }

    if (!selectedPaymentMethod) {
        alert('Please select a payment method');
        return;
    }

    try {
        // Generate unique ticket ID
        const timestamp = Date.now();
        const ticketId = `TKT-${timestamp}`;

        // Save photo to file system first
        const photoResult = await ipcRenderer.invoke('save-ticket-photo', capturedPhotoData, ticketId);

        if (!photoResult.success) {
            alert('Error saving ticket photo: ' + photoResult.message);
            return;
        }

        // Get ticket price from user's location or use default
        let ticketPrice = 56.00; // Default theater ticket price

        if (currentUser && currentUser.location_id) {
            try {
                // Fetch location-specific ticket price
                const locationResult = await ipcRenderer.invoke('get-location-by-id', currentUser.location_id);
                if (locationResult.success && locationResult.location && locationResult.location.theater_ticket_price) {
                    ticketPrice = parseFloat(locationResult.location.theater_ticket_price);
                    console.log(`Using location-specific ticket price: $${ticketPrice}`);
                } else {
                    console.log('No location-specific price found, using default: $56.00');
                }
            } catch (error) {
                console.error('Error fetching location ticket price, using default:', error);
            }
        }

        // Prepare ticket data for database
        const ticketData = {
            ticket_id: ticketId,
            duration: parseInt(duration),
            photo_path: photoResult.photoPath,
            payment_method: selectedPaymentMethod,
            ticket_price: ticketPrice,
            total_amount: ticketPrice,
            user_id: currentUser ? currentUser.id : null,
            location_id: currentUser ? currentUser.location_id : null,
            operator_name: currentUser ? currentUser.name || currentUser.username : 'Unknown',
            location_name: currentUser ? currentUser.location_name : 'Unknown Location'
        };

        console.log('Creating ticket with data:', ticketData);

        // Save ticket to database
        const result = await ipcRenderer.invoke('create-ticket', ticketData);

        if (result.success) {
            // Show success modal with ticket details
            showSuccessModal({
                ticketId: ticketId,
                duration: duration,
                price: ticketPrice,
                paymentMethod: selectedPaymentMethod.toUpperCase(),
                operator: ticketData.operator_name,
                location: ticketData.location_name,
                datetime: dayjs().format('M/D/YYYY, h:mm:ss A')
            });

            console.log('Ticket created successfully:', result.ticket);

            // Don't reset form - keep all details for multiple tickets
            // Only the ticket ID will auto-increment for next ticket
        } else {
            alert('❌ Error creating ticket: ' + result.message);
        }

    } catch (error) {
        console.error('Error issuing ticket:', error);
        alert('An error occurred while issuing the ticket. Please try again.');
    }
}

async function resetTicketForm() {
    // Reload duration from location
    await loadLocationDuration();

    // Clear photo
    clearPhoto();

    // Clear payment selection from both old and new button classes
    document.querySelectorAll('.payment-btn, .big-payment-btn').forEach(btn => {
        btn.classList.remove('selected');
    });
    selectedPaymentMethod = null;

    console.log('Ticket form reset');
}

// Ticket List Functions
let allTickets = [];
let filteredTickets = [];

async function loadTicketList(forceRefresh = false) {
    const ticketsGrid = document.getElementById('tickets-grid');

    if (!ticketsGrid) {
        console.error('Tickets grid not found');
        return;
    }

    // Show loading state
    ticketsGrid.innerHTML = `
        <div class="loading-placeholder">
            <div class="loading-spinner"></div>
            <p>Loading tickets...</p>
        </div>
    `;

    try {
        console.log('Loading tickets from database...');
        const result = await ipcRenderer.invoke('get-all-tickets');

        if (result.success) {
            allTickets = result.tickets;
            filteredTickets = [...allTickets];
            await renderTicketCards();
            console.log('Loaded tickets:', allTickets.length);
        } else {
            console.error('Error loading tickets:', result.message);
            showEmptyState('Error loading tickets');
        }
    } catch (error) {
        console.error('Error loading tickets:', error);
        showEmptyState('Failed to load tickets');
    }
}

async function renderTicketCards() {
    const ticketsGrid = document.getElementById('tickets-grid');

    if (!ticketsGrid) {
        console.error('Tickets grid not found');
        return;
    }

    if (filteredTickets.length === 0) {
        showEmptyState('No tickets found');
        return;
    }

    // Show loading while generating cards
    ticketsGrid.innerHTML = `
        <div class="loading-placeholder">
            <div class="loading-spinner"></div>
            <p>Preparing ticket cards...</p>
        </div>
    `;

    try {
        // Create all ticket cards asynchronously
        const cardPromises = filteredTickets.map(ticket => createTicketCard(ticket));
        const cardsHTML = await Promise.all(cardPromises);
        ticketsGrid.innerHTML = cardsHTML.join('');

        // Start real-time updates after rendering
        setTimeout(() => {
            startTicketStatusUpdater();
        }, 100);
    } catch (error) {
        console.error('Error rendering ticket cards:', error);
        showEmptyState('Error displaying tickets');
    }
}

async function createTicketCard(ticket) {
    // Use Day.js for reliable time calculations
    const now = dayjs();
    const issuedAt = dayjs(ticket.issued_at); // Day.js handles SQLite format automatically
    const expiresAt = issuedAt.add(ticket.duration, 'minute');

    const isExpired = now.isAfter(expiresAt);
    const isActive = !isExpired;

    // Determine card status class - prioritize database status over time calculation
    let statusClass, statusText, statusBadgeClass;
    let remainingMinutes, remainingHours, remainingMins;

    if (ticket.status === 'banned') {
        statusClass = 'banned';
        statusText = 'Banned';
        statusBadgeClass = 'status-banned';
        // Set remaining time to 0 for banned tickets
        remainingMinutes = 0;
        remainingHours = 0;
        remainingMins = 0;
    } else if (ticket.status === 'refunded') {
        statusClass = 'refunded';
        statusText = 'Refunded';
        statusBadgeClass = 'status-refunded';
        // Set remaining time to 0 for refunded tickets
        remainingMinutes = 0;
        remainingHours = 0;
        remainingMins = 0;
    } else if (isActive) {
        statusClass = 'active';
        statusText = 'Active';
        statusBadgeClass = 'status-active';
        // Calculate normal remaining time for active tickets
        remainingMinutes = expiresAt.diff(now, 'minute');
        remainingHours = Math.floor(remainingMinutes / 60);
        remainingMins = remainingMinutes % 60;
    } else {
        statusClass = 'expired';
        statusText = 'Expired';
        statusBadgeClass = 'status-expired';
        // Set remaining time to 0 for expired tickets
        remainingMinutes = 0;
        remainingHours = 0;
        remainingMins = 0;
    }

    // Format times using Day.js
    const issuedTime = issuedAt.format('HH:mm:ss');
    const expiresTime = expiresAt.format('HH:mm:ss');

    // Format remaining time
    let remainingTimeText;
    if (isActive) {
        if (remainingHours > 0) {
            remainingTimeText = `${remainingHours}h ${remainingMins}m`;
        } else {
            remainingTimeText = `${remainingMinutes}m`;
        }
    } else {
        remainingTimeText = 'Expired';
    }

    // Photo path handling - get full path for proper display
    let photoHTML = '';
    if (ticket.photo_path) {
        try {
            const photoResult = await ipcRenderer.invoke('get-ticket-photo-path', ticket.photo_path);

            if (photoResult.success) {
                const photoSrc = `file://${photoResult.fullPath}`;
                photoHTML = `<img src="${photoSrc}" alt="Visitor Photo" class="ticket-photo"
                                onerror="this.onerror=null; this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="ticket-photo" style="background: #f3f4f6; display: none; align-items: center; justify-content: center; color: #9ca3af;">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                                </svg>
                            </div>`;
            } else {
                photoHTML = `<div class="ticket-photo" style="background: #f3f4f6; display: flex; align-items: center; justify-content: center; color: #9ca3af;">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                                </svg>
                            </div>`;
            }
        } catch (error) {
            console.error('Error getting photo path for ticket:', ticket.ticket_id, error);
            photoHTML = `<div class="ticket-photo" style="background: #f3f4f6; display: flex; align-items: center; justify-content: center; color: #9ca3af;">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                        </div>`;
        }
    } else {
        photoHTML = `<div class="ticket-photo" style="background: #f3f4f6; display: flex; align-items: center; justify-content: center; color: #9ca3af;">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                    </div>`;
    }

    const isClickable = isActive && ticket.status !== 'banned' && ticket.status !== 'refunded';
    const clickHandler = isClickable ? `onclick="openTicketManagementModal(${JSON.stringify(ticket).replace(/"/g, '&quot;')})"` : '';
    const cursorStyle = isClickable ? 'cursor: pointer;' : '';

    return `
        <div class="ticket-card ${statusClass}" id="ticket-card-${ticket.id}" data-ticket-id="${ticket.id}" data-issued-at="${ticket.issued_at}" data-duration="${ticket.duration}" ${clickHandler} style="${cursorStyle}">
            <div class="ticket-header">
                <h3 class="ticket-id">${ticket.ticket_id}</h3>
                <span class="ticket-status ${statusBadgeClass}" id="status-${ticket.id}">${statusText}</span>
            </div>

            <div class="ticket-photo-container">
                ${photoHTML}
            </div>

            <div class="ticket-times">
                <span>Issued: ${issuedTime}</span>
                <span>Expires: ${expiresTime}</span>
            </div>
        </div>
    `;
}

function showEmptyState(message = 'No tickets available') {
    const ticketsGrid = document.getElementById('tickets-grid');

    if (!ticketsGrid) {
        console.error('Tickets grid not found');
        return;
    }

    ticketsGrid.innerHTML = `
        <div class="empty-state">
            <svg class="empty-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M2 6a2 2 0 012-2h16a2 2 0 012 2v2H2V6zM2 10h20v8a2 2 0 01-2 2H4a2 2 0 01-2-2v-8z"/>
            </svg>
            <h3 class="empty-title">No Tickets Found</h3>
            <p class="empty-description">${message}</p>
        </div>
    `;
}

async function filterTickets() {
    const statusFilter = document.getElementById('status-filter');

    if (!statusFilter) {
        console.error('Status filter not found');
        return;
    }

    const filterValue = statusFilter.value;
    const now = new Date();

    filteredTickets = allTickets.filter(ticket => {
        if (filterValue === 'all') return true;

        const issuedAt = new Date(ticket.issued_at);
        const durationMs = ticket.duration * 60 * 1000;
        const expiresAt = new Date(issuedAt.getTime() + durationMs);
        const isExpired = now > expiresAt;

        if (filterValue === 'active') return !isExpired;
        if (filterValue === 'expired') return isExpired;

        return true;
    });

    await renderTicketCards();
}

// Manual reset function for the reset button
function manualResetForm() {
    const confirmReset = confirm('🔄 Are you sure you want to reset the form?\n\nThis will clear:\n• Selected payment method\n• Captured photo\n• All form data\n\nClick OK to reset or Cancel to keep current data.');

    if (confirmReset) {
        resetTicketForm();
        alert('✅ Form has been reset successfully!\n\nYou can now configure new ticket settings.');
    }
}

// Real-time ticket status updater
let ticketUpdateInterval = null;

function startTicketStatusUpdater() {
    // Clear any existing interval
    if (ticketUpdateInterval) {
        clearInterval(ticketUpdateInterval);
    }

    // Update every 5 seconds for more responsive status updates
    ticketUpdateInterval = setInterval(() => {
        updateTicketStatuses();
    }, 5000);

    // Also update immediately
    updateTicketStatuses();
}

function stopTicketStatusUpdater() {
    if (ticketUpdateInterval) {
        clearInterval(ticketUpdateInterval);
        ticketUpdateInterval = null;
    }
}

async function updateTicketStatuses() {
    try {
        // Reload ticket data from database to get current status
        console.log('Updating ticket statuses from database...');
        const result = await ipcRenderer.invoke('get-all-tickets');

        if (!result.success) {
            console.error('Failed to reload tickets for status update:', result.message);
            return;
        }

        // Update the global tickets array
        allTickets = result.tickets;

        let activeCount = 0;
        let expiredCount = 0;
        let bannedCount = 0;
        let refundedCount = 0;

        allTickets.forEach(ticket => {
            const card = document.getElementById(`ticket-card-${ticket.id}`);
            if (!card) return; // Skip if card doesn't exist

            // Use Day.js for time calculations
            const now = dayjs();
            const issuedAt = dayjs(ticket.issued_at);
            const expiresAt = issuedAt.add(ticket.duration, 'minute');
            const isExpired = now.isAfter(expiresAt);
            const isActive = !isExpired;

            // Determine status based on database status (prioritize over time)
            let statusClass, statusText, statusBadgeClass;
            let remainingMinutes, remainingHours, remainingMins, remainingTimeText;

            if (ticket.status === 'banned') {
                statusClass = 'banned';
                statusText = 'Banned';
                statusBadgeClass = 'status-banned';
                remainingTimeText = 'Banned';
                bannedCount++;
            } else if (ticket.status === 'refunded') {
                statusClass = 'refunded';
                statusText = 'Refunded';
                statusBadgeClass = 'status-refunded';
                remainingTimeText = 'Refunded';
                refundedCount++;
            } else if (isActive) {
                statusClass = 'active';
                statusText = 'Active';
                statusBadgeClass = 'status-active';
                remainingMinutes = expiresAt.diff(now, 'minute');
                remainingHours = Math.floor(remainingMinutes / 60);
                remainingMins = remainingMinutes % 60;

                if (remainingHours > 0) {
                    remainingTimeText = `${remainingHours}h ${remainingMins}m`;
                } else {
                    remainingTimeText = `${remainingMinutes}m`;
                }
                activeCount++;
            } else {
                statusClass = 'expired';
                statusText = 'Expired';
                statusBadgeClass = 'status-expired';
                remainingTimeText = 'Expired';
                expiredCount++;
            }

            // Update card elements
            const statusElement = document.getElementById(`status-${ticket.id}`);

            if (statusElement) {
                // Update status badge
                statusElement.textContent = statusText;
                statusElement.className = `ticket-status ${statusBadgeClass}`;

                // Update card class
                card.className = `ticket-card ${statusClass}`;

                // Update click handler based on status
                const isClickable = statusClass === 'active';
                if (isClickable) {
                    card.style.cursor = 'pointer';
                    card.onclick = () => openTicketManagementModal(ticket);
                } else {
                    card.style.cursor = 'default';
                    card.onclick = null;
                }
            }
        });

        // Statistics removed - no longer updating count elements

        console.log(`Status update complete: ${activeCount} active, ${expiredCount} expired, ${bannedCount} banned, ${refundedCount} refunded`);

    } catch (error) {
        console.error('Error updating ticket statuses:', error);
    }
}

// Ticket Management Modal Functions
let currentSelectedTicket = null;

function openTicketManagementModal(ticket) {
    currentSelectedTicket = ticket;
    const modal = document.getElementById('ticket-management-modal');
    const photoElement = document.getElementById('modal-ticket-photo');
    const titleElement = document.getElementById('modal-ticket-title');

    // Set modal title with ticket ID
    titleElement.textContent = `Manage Ticket: ${ticket.ticket_id}`;

    // Load and display the ticket photo
    if (ticket.photo_path) {
        // Get the full photo path
        ipcRenderer.invoke('get-ticket-photo-path', ticket.photo_path)
            .then(result => {
                if (result.success) {
                    photoElement.src = `file://${result.fullPath}`;
                } else {
                    // Show placeholder if photo not found
                    photoElement.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwQzE2MS4wNDYgMTAwIDE3MCA5MS4wNDU3IDE3MCA4MEM1NyA2OC45NTQzIDE2MS4wNDYgNjAgMTUwIDYwQzEzOC45NTQgNjAgMTMwIDY4Ljk1NDMgMTMwIDgwQzEzMCA5MS4wNDU3IDEzOC45NTQgMTAwIDE1MCAxMDBaTTE1MCAxMjBDMTM2LjY2NyAxMjAgMTEwIDEyNi42NjcgMTEwIDEzMFYxNDBIMTkwVjEzMEMxOTAgMTI2LjY2NyAxNjMuMzMzIDEyMCAxNTAgMTIwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
                }
            })
            .catch(error => {
                console.error('Error loading photo:', error);
                photoElement.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwQzE2MS4wNDYgMTAwIDE3MCA5MS4wNDU3IDE3MCA4MEM1NyA2OC45NTQzIDE2MS4wNDYgNjAgMTUwIDYwQzEzOC45NTQgNjAgMTMwIDY4Ljk1NDMgMTMwIDgwQzEzMCA5MS4wNDU3IDEzOC45NTQgMTAwIDE1MCAxMDBaTTE1MCAxMjBDMTM2LjY2NyAxMjAgMTEwIDEyNi42NjcgMTEwIDEzMFYxNDBIMTkwVjEzMEMxOTAgMTI2LjY2NyAxNjMuMzMzIDEyMCAxNTAgMTIwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
            });
    } else {
        // Show placeholder if no photo
        photoElement.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwQzE2MS4wNDYgMTAwIDE3MCA5MS4wNDU3IDE3MCA4MEM1NyA2OC45NTQzIDE2MS4wNDYgNjAgMTUwIDYwQzEzOC45NTQgNjAgMTMwIDY4Ljk1NDMgMTMwIDgwQzEzMCA5MS4wNDU3IDEzOC45NTQgMTAwIDE1MCAxMDBaTTE1MCAxMjBDMTM2LjY2NyAxMjAgMTEwIDEyNi42NjcgMTEwIDEzMFYxNDBIMTkwVjEzMEMxOTAgMTI2LjY2NyAxNjMuMzMzIDEyMCAxNTAgMTIwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
    }

    // Clear form
    document.getElementById('ban-reason').value = '';
    document.getElementById('action-ban').checked = false;
    document.getElementById('action-grant').checked = false;

    // Show modal
    modal.style.display = 'flex';

    // Add click outside to close functionality
    modal.addEventListener('click', function(event) {
        if (event.target === modal) {
            closeTicketManagementModal();
        }
    });
}

function closeTicketManagementModal() {
    const modal = document.getElementById('ticket-management-modal');
    modal.style.display = 'none';
    currentSelectedTicket = null;
}

async function handleRefund() {
    if (!currentSelectedTicket) {
        alert('No ticket selected');
        return;
    }

    const confirmRefund = confirm(`Are you sure you want to refund ticket ${currentSelectedTicket.ticket_id}?\n\nThis action cannot be undone.`);

    if (confirmRefund) {
        try {
            let bannedTicketId = null;

            // First ban the ticket if not already banned
            if (currentSelectedTicket.status !== 'banned') {
                const banData = {
                    banned_by_user_id: currentUser?.id || null,
                    banned_by_operator: currentUser?.username || 'System',
                    ban_reason: 'Refunded by operator',
                    action_type: 'ban'
                };

                const banResult = await ipcRenderer.invoke('ban-ticket', currentSelectedTicket.id, banData);

                if (!banResult.success) {
                    throw new Error(banResult.message);
                }

                bannedTicketId = banResult.data.id;
            } else {
                // If already banned, we need to find the banned ticket record
                const bannedTicketsResult = await ipcRenderer.invoke('get-banned-tickets');
                if (bannedTicketsResult.success) {
                    const bannedTicket = bannedTicketsResult.data.find(bt => bt.original_ticket_id === currentSelectedTicket.id);
                    if (bannedTicket) {
                        bannedTicketId = bannedTicket.id;
                    }
                }
            }

            if (!bannedTicketId) {
                throw new Error('Could not find banned ticket record for refund');
            }

            // Then process refund
            const refundData = {
                refund_amount: calculateRefundAmount(currentSelectedTicket),
                refunded_by_user_id: currentUser?.id || null,
                refunded_by_operator: currentUser?.username || 'System'
            };

            const refundResult = await ipcRenderer.invoke('refund-banned-ticket', bannedTicketId, refundData);

            if (refundResult.success) {
                alert(`Ticket ${currentSelectedTicket.ticket_id} has been refunded successfully!\n\nRefund Amount: $${refundData.refund_amount}`);

                // Immediately update ticket statuses to reflect the change
                await updateTicketStatuses();
                closeTicketManagementModal();
            } else {
                throw new Error(refundResult.message);
            }

        } catch (error) {
            console.error('Error processing refund:', error);
            alert('Error processing refund: ' + error.message);
        }
    }
}

function calculateRefundAmount(ticket) {
    // Use actual ticket price for refund calculation
    const ticketPrice = ticket.total_amount || ticket.ticket_price || 56.00;
    return ticketPrice.toFixed(2);
}

async function handleSave() {
    if (!currentSelectedTicket) {
        alert('No ticket selected');
        return;
    }

    const reason = document.getElementById('ban-reason').value.trim();
    const actionBan = document.getElementById('action-ban').checked;
    const actionGrant = document.getElementById('action-grant').checked;

    if (!reason) {
        alert('Please enter a reason for your decision');
        return;
    }

    if (!actionBan && !actionGrant) {
        alert('Please select either Ban or Grant');
        return;
    }

    const action = actionBan ? 'ban' : 'grant';

    try {
        if (action === 'ban') {
            const banData = {
                banned_by_user_id: currentUser?.id || null,
                banned_by_operator: currentUser?.username || 'System',
                ban_reason: reason,
                action_type: action
            };

            const result = await ipcRenderer.invoke('ban-ticket', currentSelectedTicket.id, banData);

            if (result.success) {
                alert(`Ticket ${currentSelectedTicket.ticket_id} has been banned successfully!`);

                // Immediately update ticket statuses to reflect the change
                await updateTicketStatuses();
                closeTicketManagementModal();
            } else {
                throw new Error(result.message);
            }
        } else {
            // Grant action - for now just show message
            alert(`Grant action for ticket ${currentSelectedTicket.ticket_id} has been recorded.\n\nReason: ${reason}`);
            closeTicketManagementModal();
        }

    } catch (error) {
        console.error('Error saving action:', error);
        alert('Error saving action: ' + error.message);
    }
}

// Banned Tickets Functions
let allBannedTickets = [];

async function loadBannedTicketList() {
    try {
        const result = await ipcRenderer.invoke('get-banned-tickets');

        if (result.success) {
            allBannedTickets = result.data;
            await displayBannedTickets(allBannedTickets);
            updateBannedTicketStats(allBannedTickets);
        } else {
            console.error('Error loading banned tickets:', result.message);
            displayBannedTicketsError('Failed to load banned tickets: ' + result.message);
        }
    } catch (error) {
        console.error('Error loading banned tickets:', error);
        displayBannedTicketsError('Error loading banned tickets: ' + error.message);
    }
}

async function displayBannedTickets(bannedTickets) {
    const container = document.getElementById('banned-tickets-container');

    if (!container) {
        console.error('Banned tickets container not found');
        return;
    }

    if (bannedTickets.length === 0) {
        container.innerHTML = `
            <div class="no-tickets-message">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <line x1="4.93" y1="4.93" x2="19.07" y2="19.07"/>
                </svg>
                <h3>No Banned Tickets</h3>
                <p>No tickets have been banned yet.</p>
            </div>
        `;
        return;
    }

    // Create cards asynchronously to handle photo loading
    const ticketCardPromises = bannedTickets.map(ticket => createBannedTicketCard(ticket));
    const ticketCards = await Promise.all(ticketCardPromises);

    container.innerHTML = ticketCards.join('');
    container.className = 'banned-tickets-grid';
}

async function createBannedTicketCard(bannedTicket) {
    const bannedAt = dayjs(bannedTicket.banned_at);
    const issuedAt = dayjs(bannedTicket.issued_at);

    const refundStatus = bannedTicket.is_refunded ? 'Refunded' : 'Not Refunded';
    const refundClass = bannedTicket.is_refunded ? 'refunded' : 'not-refunded';

    let photoHTML;
    if (bannedTicket.photo_path) {
        try {
            console.log('Loading banned ticket photo:', bannedTicket.photo_path);
            // Use the same photo path resolution as current view list
            const photoResult = await ipcRenderer.invoke('get-ticket-photo-path', bannedTicket.photo_path);
            console.log('Photo result for banned ticket:', photoResult);

            if (photoResult.success) {
                photoHTML = `<img src="file://${photoResult.fullPath}" alt="Customer Photo" class="banned-ticket-photo" onerror="console.log('Failed to load banned ticket photo:', '${photoResult.fullPath}'); this.style.display='none'; this.nextElementSibling.style.display='flex';">
                             <div class="banned-ticket-photo-placeholder" style="display: none;">
                                 <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                                     <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                                 </svg>
                             </div>`;
            } else {
                console.log('Photo result failed for banned ticket:', photoResult.message);
                photoHTML = `<div class="banned-ticket-photo-placeholder">
                                 <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                                     <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                                 </svg>
                             </div>`;
            }
        } catch (error) {
            console.error('Error loading banned ticket photo:', error);
            photoHTML = `<div class="banned-ticket-photo-placeholder">
                             <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                                 <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                             </svg>
                         </div>`;
        }
    } else {
        console.log('No photo path for banned ticket:', bannedTicket.ticket_id);
        photoHTML = `<div class="banned-ticket-photo-placeholder">
                         <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                             <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                         </svg>
                     </div>`;
    }

    return `
        <div class="banned-ticket-card">
            <div class="banned-ticket-header">
                <h3 class="banned-ticket-id">${bannedTicket.ticket_id}</h3>
                <span class="banned-ticket-status ${refundClass}">${refundStatus}</span>
            </div>

            <div class="banned-ticket-body">
                <div class="banned-ticket-photo-container">
                    ${photoHTML}
                </div>

                <div class="banned-ticket-times">
                    <div class="banned-time-item">
                        <div class="banned-time-label">Banned Date</div>
                        <div class="banned-time-value">${bannedAt.format('MM/DD/YY')}<br>${bannedAt.format('HH:mm')}</div>
                    </div>
                </div>

                <div class="banned-ticket-reason">
                    <span class="banned-reason-label">Ban Reason</span>
                    <p class="banned-reason-text">${bannedTicket.ban_reason}</p>
                </div>

                ${bannedTicket.is_refunded ? `
                    <div class="refund-info">
                        <div class="refund-info-title">✓ Refund Processed</div>
                        <div class="banned-detail-item">
                            <span class="banned-detail-label">Amount</span>
                            <span class="banned-detail-value">$${bannedTicket.refund_amount || '0.00'}</span>
                        </div>
                        <div class="banned-detail-item">
                            <span class="banned-detail-label">Refunded</span>
                            <span class="banned-detail-value">${dayjs(bannedTicket.refunded_at).format('MM/DD/YY HH:mm')}</span>
                        </div>
                        <div class="banned-detail-item">
                            <span class="banned-detail-label">Refunded By</span>
                            <span class="banned-detail-value">${bannedTicket.refunded_by_operator || 'System'}</span>
                        </div>
                    </div>
                ` : `
                    <div class="banned-ticket-actions">
                        <button class="refund-banned-btn" onclick="refundBannedTicket(${bannedTicket.id})">
                            💰 Process Refund
                        </button>
                    </div>
                `}
            </div>
        </div>
    `;
}

function updateBannedTicketStats(bannedTickets) {
    const totalBanned = bannedTickets.length;
    const refunded = bannedTickets.filter(ticket => ticket.is_refunded).length;
    const pending = totalBanned - refunded;

    const bannedCountEl = document.getElementById('banned-count');
    const refundedCountEl = document.getElementById('refunded-count');
    const pendingCountEl = document.getElementById('pending-count');

    if (bannedCountEl) bannedCountEl.textContent = totalBanned;
    if (refundedCountEl) refundedCountEl.textContent = refunded;
    if (pendingCountEl) pendingCountEl.textContent = pending;
}

function displayBannedTicketsError(message) {
    const container = document.getElementById('banned-tickets-container');
    if (container) {
        container.innerHTML = `
            <div class="error-message">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <line x1="15" y1="9" x2="9" y2="15"/>
                    <line x1="9" y1="9" x2="15" y2="15"/>
                </svg>
                <h3>Error Loading Banned Tickets</h3>
                <p>${message}</p>
                <button onclick="loadBannedTicketList()" class="retry-btn">Retry</button>
            </div>
        `;
    }
}

async function refundBannedTicket(bannedTicketId) {
    const bannedTicket = allBannedTickets.find(ticket => ticket.id === bannedTicketId);
    if (!bannedTicket) {
        alert('Banned ticket not found');
        return;
    }

    const confirmRefund = confirm(`Are you sure you want to process a refund for ticket ${bannedTicket.ticket_id}?\n\nThis action cannot be undone.`);

    if (confirmRefund) {
        try {
            const refundAmount = calculateRefundAmount(bannedTicket);
            const refundData = {
                refund_amount: refundAmount,
                refunded_by_user_id: currentUser?.id || null,
                refunded_by_operator: currentUser?.username || 'System'
            };

            const result = await ipcRenderer.invoke('refund-banned-ticket', bannedTicketId, refundData);

            if (result.success) {
                alert(`Refund processed successfully!\n\nTicket: ${bannedTicket.ticket_id}\nAmount: $${refundAmount}`);

                // Refresh the banned tickets list
                await loadBannedTicketList();
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Error processing refund:', error);
            alert('Error processing refund: ' + error.message);
        }
    }
}

// Success Modal Functions
function showSuccessModal(ticketDetails) {
    const modal = document.getElementById('ticket-success-modal');

    // Populate ticket details
    document.getElementById('success-ticket-id').textContent = ticketDetails.ticketId;
    document.getElementById('success-duration').textContent = `${ticketDetails.duration} minutes`;
    document.getElementById('success-price').textContent = `$${ticketDetails.price.toFixed(2)}`;
    document.getElementById('success-payment').textContent = ticketDetails.paymentMethod;
    document.getElementById('success-operator').textContent = ticketDetails.operator;
    document.getElementById('success-location').textContent = ticketDetails.location;
    document.getElementById('success-datetime').textContent = ticketDetails.datetime;

    // Show modal
    modal.style.display = 'flex';

    // Add click outside to close functionality
    modal.addEventListener('click', function(event) {
        if (event.target === modal) {
            closeSuccessModal();
        }
    });

    // Auto close after 10 seconds (optional)
    setTimeout(() => {
        if (modal.style.display === 'flex') {
            // Only auto-close if still open
            console.log('Auto-closing success modal after 10 seconds');
        }
    }, 10000);

    console.log('Success modal displayed with ticket details:', ticketDetails);
}

function closeSuccessModal() {
    const modal = document.getElementById('ticket-success-modal');
    modal.style.display = 'none';

    console.log('Success modal closed');
}
