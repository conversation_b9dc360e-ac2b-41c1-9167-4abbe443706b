/**
 * Test script for the enhanced automatic stock deduction system
 * Tests both location_stocks.stock and products.max_qty updates
 */

const Database = require('./src/database.js');
const InventoryService = require('./src/services/inventoryService.js');

class StockDeductionTester {
    constructor() {
        this.db = new Database();
        this.inventoryService = null;
    }

    async init() {
        console.log('🔧 Initializing test environment...');

        // Use existing database file for testing
        const sqlite3 = require('sqlite3').verbose();
        const path = require('path');
        const fs = require('fs');

        // Use the actual database file
        const possiblePaths = [
            './src/pos_system.db',
            './pos_database.db',
            './pos_system.db'
        ];

        let dbPath = null;
        for (const testPath of possiblePaths) {
            if (fs.existsSync(testPath)) {
                dbPath = testPath;
                break;
            }
        }

        if (!dbPath) {
            // Create a temporary database for testing
            dbPath = './test_pos_system.db';
            console.log('📝 Creating temporary test database...');
        } else {
            console.log(`📂 Using existing database: ${dbPath}`);
        }

        // Initialize database connection directly
        this.db.db = new sqlite3.Database(dbPath, (err) => {
            if (err) {
                console.error('Error opening database:', err);
                throw err;
            } else {
                console.log('Connected to SQLite database');
            }
        });

        // Wait for database to be ready
        await new Promise(resolve => setTimeout(resolve, 500));

        // Initialize inventory service
        this.inventoryService = new InventoryService(this.db);
        console.log('✅ Test environment ready');
    }

    async findTestProduct() {
        console.log('\n📦 Finding existing product for testing...');

        try {
            // Get any existing product
            const products = await new Promise((resolve, reject) => {
                this.db.db.all(`
                    SELECT p.id, p.barcode, p.description, p.max_qty, ls.location, ls.stock
                    FROM products p
                    JOIN location_stocks ls ON p.id = ls.product_id
                    LIMIT 1
                `, (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                });
            });

            if (products.length === 0) {
                throw new Error('No products found in database');
            }

            const product = products[0];
            console.log('✅ Found test product:', {
                id: product.id,
                barcode: product.barcode,
                description: product.description,
                max_qty: product.max_qty,
                location: product.location,
                stock: product.stock
            });

            // Add some stock for testing if needed
            if (product.max_qty < 50 || product.stock < 10) {
                console.log('📈 Adding stock for testing...');

                // Update global stock
                await new Promise((resolve, reject) => {
                    this.db.db.run(`
                        UPDATE products
                        SET max_qty = 100, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    `, [product.id], (err) => {
                        if (err) reject(err);
                        else resolve();
                    });
                });

                // Update location stock
                await new Promise((resolve, reject) => {
                    this.db.db.run(`
                        UPDATE location_stocks
                        SET stock = 50, updated_at = CURRENT_TIMESTAMP
                        WHERE product_id = ? AND location = ?
                    `, [product.id, product.location], (err) => {
                        if (err) reject(err);
                        else resolve();
                    });
                });

                console.log('✅ Stock updated for testing (global: 100, location: 50)');
            }

            return { productId: product.id, location: product.location };
        } catch (error) {
            console.error('❌ Error finding test product:', error);
            throw error;
        }
    }

    async testStockDeduction(productId, locationName) {
        console.log('\n🧪 Testing stock deduction...');

        try {
            // Get initial stock levels
            const initialStock = await this.db.getProductStockDetails(productId);
            console.log('📊 Initial stock levels:');
            console.log(`   Global stock (max_qty): ${initialStock.global_stock}`);

            const locationStock = initialStock.locations.find(loc => loc.location === locationName);
            console.log(`   Location stock (${locationName}): ${locationStock?.stock || 0}`);

            // Test single item deduction
            console.log('\n🔄 Testing single item deduction (quantity: 2)...');
            const result = await this.inventoryService.reduceStock(productId, 2, locationName);

            console.log('✅ Stock deduction result:', {
                newLocationStock: result.newLocationStock,
                newGlobalStock: result.newGlobalStock,
                alerts: result.alerts?.length || 0
            });

            // Verify the changes
            const updatedStock = await this.db.getProductStockDetails(productId);
            console.log('📊 Updated stock levels:');
            console.log(`   Global stock (max_qty): ${updatedStock.global_stock}`);

            const updatedLocationStock = updatedStock.locations.find(loc => loc.location === locationName);
            console.log(`   Location stock (${locationName}): ${updatedLocationStock?.stock || 0}`);

            // Validate the deduction
            const expectedGlobalStock = initialStock.global_stock - 2;
            const expectedLocationStock = (locationStock?.stock || 0) - 2;

            if (updatedStock.global_stock === expectedGlobalStock &&
                (updatedLocationStock?.stock || 0) === expectedLocationStock) {
                console.log('✅ Stock deduction validation PASSED');
                return true;
            } else {
                console.log('❌ Stock deduction validation FAILED');
                console.log(`   Expected global: ${expectedGlobalStock}, Actual: ${updatedStock.global_stock}`);
                console.log(`   Expected location: ${expectedLocationStock}, Actual: ${updatedLocationStock?.stock || 0}`);
                return false;
            }

        } catch (error) {
            console.error('❌ Error testing stock deduction:', error);
            return false;
        }
    }

    async testMultipleItemSale(productId, locationName) {
        console.log('\n🛒 Testing multiple item sale simulation...');

        const saleItems = [
            { productId: productId, name: 'Test Product', quantity: 1 },
            { productId: productId, name: 'Test Product', quantity: 1 }
        ];

        try {
            const result = await this.inventoryService.updateInventoryAfterSale(saleItems, locationName);
            console.log('✅ Multiple item sale result:', {
                success: result.success,
                itemsProcessed: result.itemsProcessed,
                alerts: result.alerts?.length || 0
            });

            return result.success;
        } catch (error) {
            console.error('❌ Error testing multiple item sale:', error);
            return false;
        }
    }

    async testInsufficientStock(productId, locationName) {
        console.log('\n⚠️ Testing insufficient stock scenario...');

        try {
            // Try to deduct more stock than available
            await this.inventoryService.reduceStock(productId, 10000, locationName);
            console.log('❌ Should have failed with insufficient stock error');
            return false;
        } catch (error) {
            if (error.message.includes('Insufficient stock')) {
                console.log('✅ Insufficient stock validation PASSED');
                return true;
            } else {
                console.log('❌ Unexpected error:', error.message);
                return false;
            }
        }
    }

    async testStockConsistency(productId) {
        console.log('\n🔍 Testing stock consistency validation...');

        try {
            const consistency = await this.db.validateStockConsistency(productId);
            console.log('📊 Stock consistency check:', {
                totalProducts: consistency.total_products,
                inconsistencies: consistency.inconsistencies,
                details: consistency.details
            });

            return consistency.inconsistencies === 0;
        } catch (error) {
            console.error('❌ Error checking stock consistency:', error);
            return false;
        }
    }



    async runAllTests() {
        console.log('🚀 Starting Stock Deduction System Tests\n');

        let testInfo;
        const results = {
            productFound: false,
            stockDeduction: false,
            multipleItemSale: false,
            insufficientStock: false,
            stockConsistency: false
        };

        try {
            await this.init();

            // Test 1: Find existing test product
            testInfo = await this.findTestProduct();
            results.productFound = true;

            // Test 2: Basic stock deduction
            results.stockDeduction = await this.testStockDeduction(testInfo.productId, testInfo.location);

            // Test 3: Multiple item sale
            results.multipleItemSale = await this.testMultipleItemSale(testInfo.productId, testInfo.location);

            // Test 4: Insufficient stock validation
            results.insufficientStock = await this.testInsufficientStock(testInfo.productId, testInfo.location);

            // Test 5: Stock consistency check
            results.stockConsistency = await this.testStockConsistency(testInfo.productId);

        } catch (error) {
            console.error('❌ Test execution failed:', error);
        } finally {
            // No cleanup needed since we're using existing products
            this.db.close();
        }

        // Print test results
        console.log('\n📋 TEST RESULTS SUMMARY:');
        console.log('========================');
        Object.entries(results).forEach(([test, passed]) => {
            const testName = test.replace(/([A-Z])/g, ' $1').toLowerCase();
            console.log(`${passed ? '✅' : '❌'} ${testName}: ${passed ? 'PASSED' : 'FAILED'}`);
        });

        const passedTests = Object.values(results).filter(Boolean).length;
        const totalTests = Object.keys(results).length;
        console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);

        if (passedTests === totalTests) {
            console.log('🎉 All tests PASSED! Stock deduction system is working correctly.');
        } else {
            console.log('⚠️ Some tests FAILED. Please review the implementation.');
        }
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    const tester = new StockDeductionTester();
    tester.runAllTests().catch(console.error);
}

module.exports = StockDeductionTester;
