const { SupabaseSync } = require('./src/supabase-config');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

async function testSyncFix() {
    console.log('🧪 Testing Sync Fix for Foreign Key Constraint Violation\n');

    try {
        // Create a mock database object that points to the actual database file
        const dbPath = path.join(__dirname, 'src', 'pos_system.db');
        console.log('📁 Using database:', dbPath);

        const mockDatabase = {
            db: new sqlite3.Database(dbPath)
        };

        const supabaseSync = new SupabaseSync(mockDatabase);

        console.log('🔄 Testing sales_items sync specifically...\n');

        // Test just the sales_items table sync
        const result = await supabaseSync.syncTable('sales_items');
        
        console.log('\n📊 Sales Items Sync Result:');
        console.log('============================');
        console.log(`Status: ${result.success ? '✅ SUCCESS' : '❌ FAILED'}`);
        console.log(`Message: ${result.message}`);
        console.log(`Records synced: ${result.count || 0}`);
        
        if (!result.success) {
            console.log(`Error: ${result.error}`);
        } else {
            console.log('\n🎯 SUCCESS! The foreign key constraint violation has been fixed.');
            console.log('The validation code successfully filtered out orphaned sales_items records.');
        }
        
    } catch (error) {
        console.error('❌ Test failed with error:', error);
        console.error('Stack trace:', error.stack);
    }
}

// Run the test
testSyncFix().then(() => {
    console.log('\n🏁 Test completed.');
    process.exit(0);
}).catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
});
