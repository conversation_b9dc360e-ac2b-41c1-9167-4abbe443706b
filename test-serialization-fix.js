/**
 * Test the serialization fix for reorder alerts
 */

const Database = require('./src/database');
const InventoryService = require('./src/services/inventoryService');
const path = require('path');

async function testSerializationFix() {
    console.log('🧪 Testing Serialization Fix for Reorder Alerts');
    console.log('================================================\n');

    const dbPath = path.join(__dirname, 'src', 'pos_system.db');
    console.log(`Database path: ${dbPath}`);
    
    const mockDb = {
        db: null
    };
    
    const sqlite3 = require('sqlite3').verbose();
    
    return new Promise((resolve, reject) => {
        mockDb.db = new sqlite3.Database(dbPath, async (err) => {
            if (err) {
                console.error('❌ Database connection failed:', err.message);
                reject(err);
                return;
            }
            
            console.log('✅ Connected to SQLite database\n');
            
            try {
                // Test InventoryService
                const inventoryService = new InventoryService(mockDb);
                const report = await inventoryService.getReorderReport();
                
                console.log('📊 Raw Report from InventoryService:');
                console.log(`  Alerts count: ${report.alerts.length}`);
                
                if (report.alerts.length > 0) {
                    console.log('\n🔍 First alert raw data:');
                    const firstAlert = report.alerts[0];
                    console.log('  Raw alert object:', firstAlert);
                    
                    // Test the exact serialization logic from IPC handler
                    console.log('\n🔄 Testing IPC Handler Serialization Logic:');
                    
                    const simplifiedReport = {
                        timestamp: String(report.timestamp || new Date().toISOString()),
                        location: String(report.location || 'Unknown'),
                        total_products_checked: Number(report.total_products_checked || 0),
                        critical_stock_count: Number(report.critical_stock?.length || 0),
                        low_stock_count: Number(report.low_stock?.length || 0),
                        total_reorder_value: Number(report.total_reorder_value || 0),
                        alerts: (report.alerts || []).map(alert => {
                            return {
                                id: Number(alert.id) || 0,
                                barcode: String(alert.barcode || ''),
                                description: String(alert.description || ''),
                                category: String(alert.category || ''),
                                subcategory: String(alert.subcategory || ''),
                                supplier: String(alert.supplier || ''),
                                location: String(alert.location || ''),
                                stock: Number(alert.stock) || 0,
                                min_qty: Number(alert.min_qty) || 0,
                                max_qty: Number(alert.max_qty) || 0,
                                price: Number(alert.price) || 0,
                                stock_status: String(alert.stock_status || 'unknown'),
                                reorder_quantity: Number(alert.reorder_quantity) || 0,
                                days_until_stockout: alert.days_until_stockout ? Number(alert.days_until_stockout) : null,
                                last_checked: String(alert.last_checked || new Date().toISOString())
                            };
                        })
                    };
                    
                    console.log('📦 Simplified Report:');
                    console.log(`  Alerts count: ${simplifiedReport.alerts.length}`);
                    
                    if (simplifiedReport.alerts.length > 0) {
                        console.log('\n🔍 First simplified alert:');
                        const firstSimplified = simplifiedReport.alerts[0];
                        console.log('  Simplified alert object:', firstSimplified);
                        
                        // Test JSON serialization
                        console.log('\n🧪 Testing JSON Serialization:');
                        try {
                            const jsonString = JSON.stringify(simplifiedReport);
                            console.log('✅ JSON serialization successful');
                            console.log(`  JSON length: ${jsonString.length} characters`);
                            
                            // Test parsing back
                            const parsed = JSON.parse(jsonString);
                            console.log('✅ JSON parsing successful');
                            console.log(`  Parsed alerts count: ${parsed.alerts.length}`);
                            
                            if (parsed.alerts.length > 0) {
                                console.log('\n🎯 Product ID 11 Status:');
                                const product11 = parsed.alerts.find(alert => alert.id === 11);
                                if (product11) {
                                    console.log('✅ Product ID 11 found in serialized data:');
                                    console.log(`    ID: ${product11.id}`);
                                    console.log(`    Barcode: ${product11.barcode}`);
                                    console.log(`    Description: ${product11.description}`);
                                    console.log(`    Stock: ${product11.stock}`);
                                    console.log(`    Min Qty: ${product11.min_qty}`);
                                    console.log(`    Status: ${product11.stock_status}`);
                                } else {
                                    console.log('❌ Product ID 11 not found in serialized data');
                                }
                            }
                            
                        } catch (serializationError) {
                            console.error('❌ JSON serialization failed:', serializationError);
                            console.log('\n🔍 Analyzing problematic fields:');
                            
                            // Test each field individually
                            Object.keys(firstSimplified).forEach(key => {
                                try {
                                    JSON.stringify(firstSimplified[key]);
                                    console.log(`  ✅ ${key}: OK`);
                                } catch (fieldError) {
                                    console.log(`  ❌ ${key}: ${fieldError.message}`);
                                }
                            });
                        }
                    }
                } else {
                    console.log('ℹ️  No alerts found to test');
                }
                
                console.log('\n📊 Test Summary:');
                console.log('================');
                console.log(`✅ Database connection: Working`);
                console.log(`✅ InventoryService: Working`);
                console.log(`✅ Report generation: Working`);
                console.log(`📊 Alerts found: ${report.alerts.length}`);
                
                mockDb.db.close();
                resolve();
                
            } catch (error) {
                console.error('❌ Error during testing:', error);
                mockDb.db.close();
                reject(error);
            }
        });
    });
}

// Run the test
testSerializationFix().catch(console.error);
