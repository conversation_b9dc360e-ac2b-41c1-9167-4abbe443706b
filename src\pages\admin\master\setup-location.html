<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set Up Location - Rainbow Station Inc</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #f9fafb;
            color: #1f2937;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            overflow: hidden;
        }

        .admin-container {
            height: 100vh;
            display: flex;
            overflow: hidden;
        }

        /* Sidebar */
        .sidebar {
            background-color: #ffffff;
            border-right: 2px solid #e5e7eb;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            flex-shrink: 0;
            width: 256px;
        }

        .sidebar.collapsed {
            width: 64px;
        }

        .sidebar-header {
            padding: 16px;
            border-bottom: 1px solid #e5e7eb;
            height: 80px;
            display: flex;
            align-items: center;
        }

        .sidebar-header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 900;
            color: #1f2937;
        }

        .sidebar.collapsed .sidebar-title {
            display: none;
        }

        .toggle-btn {
            background-color: #ffffff;
            color: #000000;
            border: 1px solid #10b981;
            padding: 8px;
            cursor: pointer;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .toggle-btn:hover {
            background-color: #ecfdf5;
            color: #000000;
        }

        .toggle-btn svg {
            color: #000000;
        }

        /* Sidebar Navigation */
        .sidebar-nav {
            margin-top: 16px;
        }

        .nav-item {
            width: 100%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            margin: 0 8px;
            border-radius: 8px;
            transition: all 0.2s ease;
            cursor: pointer;
            border: none;
            background: none;
            text-align: left;
            color: #374151;
        }

        .nav-item:hover {
            background-color: #d1fae5;
            color: #065f46;
        }

        .nav-item.active {
            background-color: #059669;
            color: #ffffff;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .nav-item.back-to-pos {
            color: #dc2626;
        }

        .nav-item.back-to-pos:hover {
            background-color: #fef2f2;
            color: #dc2626;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
        }

        .nav-label {
            font-weight: bold;
            font-size: 14px;
            flex: 1;
            text-align: left;
        }

        .sidebar.collapsed .nav-label {
            display: none;
        }

        .dropdown-icon {
            width: 16px;
            height: 16px;
            margin-left: auto;
        }

        .sidebar.collapsed .dropdown-icon {
            display: none;
        }

        /* Dropdown Sub-items */
        .sub-items {
            margin-left: 16px;
            margin-top: 4px;
            display: block;
        }

        .sub-items.hidden {
            display: none;
        }

        .sidebar.collapsed .sub-items {
            display: none;
        }

        .sub-item {
            width: 100%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px;
            margin: 0 8px 4px 8px;
            border-radius: 8px;
            transition: all 0.2s ease;
            cursor: pointer;
            border: none;
            background: none;
            text-align: left;
            color: #6b7280;
            font-size: 14px;
        }

        .sub-item:hover {
            background-color: #d1fae5;
            color: #065f46;
        }

        .sub-item.active {
            background-color: #059669;
            color: #ffffff;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .sub-icon {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        .sub-label {
            font-weight: 500;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .content-area {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
        }

        /* Header */
        .main-header {
            background-color: #ffffff;
            border-bottom: 2px solid #e5e7eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
            padding: 16px 24px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title h1 {
            font-size: 24px;
            font-weight: 900;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .header-title p {
            font-size: 14px;
            color: #6b7280;
        }

        .header-info {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .header-info-text {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-size: 14px;
            color: #374151;
        }

        .header-controls {
            display: flex;
            gap: 8px;
        }

        .header-control-btn {
            background-color: #374151;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background-color 0.2s;
        }

        .header-control-btn:hover {
            background-color: #4b5563;
        }

        .header-control-btn.minimize:hover {
            background-color: #3b82f6;
        }

        .header-control-btn.close:hover {
            background-color: #ef4444;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 900;
            color: #1f2937;
            margin-bottom: 24px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
        }

        .btn {
            padding: 8px 32px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-green {
            background-color: #059669;
            color: white;
        }

        .btn-green:hover {
            background-color: #047857;
        }

        .btn-blue {
            background-color: #2563eb;
            color: white;
        }

        .btn-blue:hover {
            background-color: #1d4ed8;
        }

        .btn-red {
            background-color: #dc2626;
            color: white;
        }

        .btn-red:hover {
            background-color: #b91c1c;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            min-width: 60px;
        }

        .flex {
            display: flex;
        }

        .gap-2 {
            gap: 8px;
        }

        .justify-center {
            justify-content: center;
        }

        .form-card {
            background-color: #ffffff;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            padding: 24px;
            margin-bottom: 32px;
        }

        .form-grid {
            display: grid;
            gap: 24px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 14px;
            font-weight: bold;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-input, .form-select {
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #059669;
            box-shadow: 0 0 0 2px rgba(5, 150, 105, 0.2);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
            padding-top: 32px;
        }

        .checkbox {
            width: 16px;
            height: 16px;
        }

        .table-card {
            background-color: #ffffff;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            padding: 24px;
        }

        .table-container {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background-color: #2563eb;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: bold;
            border-right: 1px solid #1d4ed8;
        }

        .table th:last-child {
            border-right: none;
        }

        .table td {
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
            border-right: 1px solid #e5e7eb;
            color: #1f2937;
        }

        .table td:last-child {
            border-right: none;
        }

        .table tr:nth-child(even) {
            background-color: #f9fafb;
        }

        .table tr:hover {
            background-color: #f3f4f6;
            cursor: pointer;
        }

        .table td.font-medium {
            font-weight: 500;
        }

        .table td.text-center {
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-header-content">
                    <h2 class="sidebar-title">ADMIN PANEL</h2>
                    <button class="toggle-btn" onclick="toggleSidebar()">
                        <svg class="icon" id="toggle-icon" viewBox="0 0 24 24">
                            <path d="M3 12h18m-9-9l9 9-9 9"/>
                        </svg>
                    </button>
                </div>
            </div>

            <nav class="sidebar-nav">
                <button class="nav-item" onclick="handleNavClick('dashboard')">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                        <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                    <span class="nav-label">Dashboard</span>
                </button>

                <button class="nav-item" onclick="handleNavClick('master')">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <ellipse cx="12" cy="5" rx="9" ry="3"/>
                        <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"/>
                        <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"/>
                    </svg>
                    <span class="nav-label">Master</span>
                    <svg class="dropdown-icon" id="master-dropdown" viewBox="0 0 24 24">
                        <polyline points="6,9 12,15 18,9"/>
                    </svg>
                </button>

                <div class="sub-items" id="master-subitems">
                    <button class="sub-item active" onclick="handleNavClick('setup-location')">
                        <svg class="sub-icon" viewBox="0 0 24 24">
                            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                            <circle cx="12" cy="10" r="3"/>
                        </svg>
                        <span class="sub-label">Set Up Location</span>
                    </button>
                    <button class="sub-item" onclick="handleNavClick('setup-category')">
                        <svg class="sub-icon" viewBox="0 0 24 24">
                            <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"/>
                            <line x1="7" y1="7" x2="7.01" y2="7"/>
                        </svg>
                        <span class="sub-label">Set Up Category</span>
                    </button>
                    <button class="sub-item" onclick="handleNavClick('setup-supplier')">
                        <svg class="sub-icon" viewBox="0 0 24 24">
                            <rect x="1" y="3" width="15" height="13"/>
                            <polygon points="16,8 20,8 23,11 23,16 16,16"/>
                            <circle cx="5.5" cy="18.5" r="2.5"/>
                            <circle cx="18.5" cy="18.5" r="2.5"/>
                        </svg>
                        <span class="sub-label">Set Up Supplier</span>
                    </button>
                    <button class="sub-item" onclick="handleNavClick('setup-product')">
                        <svg class="sub-icon" viewBox="0 0 24 24">
                            <circle cx="9" cy="21" r="1"/>
                            <circle cx="20" cy="21" r="1"/>
                            <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/>
                        </svg>
                        <span class="sub-label">Set Up Product</span>
                    </button>
                </div>

                <button class="nav-item" onclick="handleNavClick('reports')">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                        <polyline points="14,2 14,8 20,8"/>
                        <line x1="16" y1="13" x2="8" y2="13"/>
                        <line x1="16" y1="17" x2="8" y2="17"/>
                        <polyline points="10,9 9,9 8,9"/>
                    </svg>
                    <span class="nav-label">Reports</span>
                </button>

                <button class="nav-item" onclick="handleNavClick('transactions')">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <rect x="1" y="4" width="22" height="16" rx="2" ry="2"/>
                        <line x1="1" y1="10" x2="23" y2="10"/>
                    </svg>
                    <span class="nav-label">Transactions</span>
                </button>

                <button class="nav-item" onclick="handleNavClick('wholesale')">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                        <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
                        <line x1="12" y1="22.08" x2="12" y2="12"/>
                    </svg>
                    <span class="nav-label">Wholesale</span>
                </button>

                <button class="nav-item" onclick="handleNavClick('user-management')">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                    <span class="nav-label">User Management</span>
                </button>

                <button class="nav-item back-to-pos" onclick="handleNavClick('back-to-pos')">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <line x1="19" y1="12" x2="5" y2="12"/>
                        <polyline points="12,19 5,12 12,5"/>
                    </svg>
                    <span class="nav-label">Back To POS</span>
                </button>
            </nav>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Header -->
            <div class="main-header">
            <div class="header-content">
                <div class="header-title">
                    <h1>SET UP LOCATION</h1>
                    <p id="location-name">Rainbow Station Inc.</p>
                </div>
                <div class="header-info">
                    <div class="header-info-text">
                        <div>Date: <span id="current-date">01/01/24</span></div>
                        <div>Time: <span id="current-time">00:00:00</span></div>
                        <div>Admin: <span id="current-operator">Loading...</span></div>
                    </div>
                    <div class="header-controls">
                        <button onclick="navigateToAdmin()" class="header-control-btn" style="background-color: #059669;" title="Back to Admin">← ADMIN</button>
                        <button onclick="toggleFullscreen()" id="fullscreen-btn" class="header-control-btn" title="Maximize Window">⛶</button>
                        <button onclick="minimizeApp()" class="header-control-btn minimize" title="Minimize">−</button>
                        <button onclick="closeApp()" class="header-control-btn close" title="Close">×</button>
                    </div>
                </div>
            </div>
        </div>

            <!-- Content Area -->
            <div class="content-area">
                <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="btn btn-green" onclick="saveLocation()">SAVE</button>
                <button class="btn btn-blue" onclick="clearForm()">CLEAR</button>
                <button class="btn btn-red" onclick="deleteLocation()">DELETE</button>
            </div>

            <!-- Main Form -->
            <div class="form-card">
                <div class="form-grid">
                    <!-- Location Code and Location Row -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Location Code</label>
                            <input type="text" class="form-input" id="locationCode" placeholder="Enter location code">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Location</label>
                            <input type="text" class="form-input" id="location" placeholder="Enter location name">
                        </div>
                    </div>

                    <!-- Company Name and E-mail Row -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Company Name</label>
                            <input type="text" class="form-input" id="companyName" placeholder="Enter company name">
                        </div>
                        <div class="form-group">
                            <label class="form-label">E-mail</label>
                            <input type="email" class="form-input" id="email" placeholder="Enter email address">
                        </div>
                    </div>

                    <!-- Address1 and App Mode Row -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Address1</label>
                            <input type="text" class="form-input" id="address1" placeholder="Enter address line 1">
                        </div>
                        <div class="form-group">
                            <label class="form-label">App Mode</label>
                            <select class="form-select" id="appMode">
                                <option value="">Select App Mode</option>
                                <option value="retail">Retail</option>
                                <option value="wholesale">Wholesale</option>
                                <option value="both">Both</option>
                            </select>
                        </div>
                    </div>

                    <!-- Address2 and Theater PLU Row -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Address2</label>
                            <input type="text" class="form-input" id="address2" placeholder="Enter address line 2">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Theater PLU</label>
                            <input type="text" class="form-input" id="theaterPLU" placeholder="Enter theater PLU">
                        </div>
                    </div>

                    <!-- Phone and Theater Time Row -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Phone</label>
                            <input type="tel" class="form-input" id="phone" placeholder="Enter phone number">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Theater Time</label>
                            <input type="text" class="form-input" id="theaterTime" placeholder="Enter theater time">
                        </div>
                    </div>

                    <!-- Theater Ticket Price Row -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Theater Ticket Price</label>
                            <input type="number" step="0.01" min="0" class="form-input" id="theaterTicketPrice" placeholder="56.00" value="56.00">
                        </div>
                        <div class="form-group">
                            <!-- Empty div to maintain grid layout -->
                        </div>
                    </div>

                    <!-- Tax% and Deli Row -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Tax%</label>
                            <input type="number" step="0.001" class="form-input" id="taxPercent" placeholder="Enter tax percentage">
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" class="checkbox" id="deli">
                            <label class="form-label" for="deli">Deli</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location Directory Table -->
            <div class="table-card">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Location Code</th>
                                <th>Location</th>
                                <th>Company Name</th>
                                <th>Address1</th>
                                <th>Address2</th>
                                <th class="text-center">Phone#</th>
                                <th class="text-center">Tax%</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="locationTableBody">
                            <!-- Table rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
            </div> <!-- Close content-area -->
        </div> <!-- Close main-content -->
    </div> <!-- Close admin-container -->

    <script src="setup-location.js"></script>
</body>
</html>
