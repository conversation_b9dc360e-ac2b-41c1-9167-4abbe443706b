const { ipc<PERSON>ender<PERSON> } = require('electron');

// Sidebar functionality
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('collapsed');
}

function handleNavClick(page) {
    switch (page) {
        case 'dashboard':
            ipc<PERSON>enderer.invoke('navigate-to-admin-section', 'dashboard');
            break;
        case 'master':
            // Toggle master dropdown
            const masterSubitems = document.getElementById('master-subitems');
            masterSubitems.classList.toggle('hidden');
            break;
        case 'setup-location':
            ipcRenderer.invoke('navigate-to-page', 'admin/master/setup-location.html');
            break;
        case 'setup-category':
            ipcRenderer.invoke('navigate-to-page', 'admin/master/setup-category.html');
            break;
        case 'setup-supplier':
            ipcRenderer.invoke('navigate-to-page', 'admin/master/setup-supplier.html');
            break;
        case 'setup-product':
            ipc<PERSON>enderer.invoke('navigate-to-page', 'admin/master/setup-product.html');
            break;
        case 'reports':
            ipc<PERSON>enderer.invoke('navigate-to-admin-section', 'reports');
            break;
        case 'transactions':
            ipcRenderer.invoke('navigate-to-admin-section', 'transactions');
            break;
        case 'wholesale':
            ipcRenderer.invoke('navigate-to-admin-section', 'wholesale');
            break;
        case 'user-management':
            ipcRenderer.invoke('navigate-to-admin-section', 'user-management');
            break;
        case 'back-to-pos':
            ipcRenderer.invoke('navigate-to', 'pos');
            break;
        default:
            console.log('Unknown navigation:', page);
    }
}

// Data loaded from database
let locationStocks = [];
let categories = [];
let suppliers = [];
let isEditMode = false;
let editingProductId = null;
let selectedImageFile = null;
let selectedImagePath = null;
let currentUser = null;

// Message display function
function showMessage(message, type = 'info') {
    // Remove existing message
    const existingMessage = document.querySelector('.message-display');
    if (existingMessage) {
        existingMessage.remove();
    }

    // Create message element
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-display message-${type}`;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: bold;
        z-index: 1000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideIn 0.3s ease-out;
    `;

    // Set background color based on type
    switch (type) {
        case 'success':
            messageDiv.style.backgroundColor = '#10b981';
            break;
        case 'error':
            messageDiv.style.backgroundColor = '#ef4444';
            break;
        case 'info':
            messageDiv.style.backgroundColor = '#3b82f6';
            break;
        default:
            messageDiv.style.backgroundColor = '#6b7280';
    }

    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);

    // Auto remove after 4 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => messageDiv.remove(), 300);
        }
    }, 4000);
}

// Add CSS animation
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Form data object
let formData = {
    barcode: "",
    description: "",
    category: "",
    subCategory: "",
    supplier: "",
    brandName: "",
    purchasePrice: "",
    style: "",
    color: "",
    size: "",
    minQty: "",
    maxQty: "",
    specialDiscount: false,
    priority: false,
    imageConfirm: false,
    nonScanable: false,
    deliItem: false
};

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    // Load current user information
    await loadCurrentUser();

    updateTime();
    setInterval(updateTime, 1000);

    // Load data from database
    await loadLocations();
    await loadCategories();
    await loadSuppliers();
    await loadProducts();

    renderLocationStockTable();

    // Initialize maximize button state
    try {
        const isMaximized = await ipcRenderer.invoke('is-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error checking initial maximize state:', error);
    }

    // Add search button event listener
    const searchButton = document.querySelector('.input-with-button .btn');
    if (searchButton) {
        searchButton.addEventListener('click', searchProduct);
    }

    // Initialize image upload functionality
    initializeImageUpload();

    // Add event listener for Image Confirm checkbox
    const imageConfirmCheckbox = document.getElementById('imageConfirm');
    if (imageConfirmCheckbox) {
        imageConfirmCheckbox.addEventListener('change', updateImageConfirmStatus);
    }
});

// Current user management
async function loadCurrentUser() {
    try {
        console.log('Setup Product - Loading current user...');
        currentUser = await ipcRenderer.invoke('get-current-user');

        if (currentUser) {
            console.log('Setup Product - User loaded:', {
                username: currentUser.username,
                role: currentUser.role,
                location: currentUser.location_name
            });

            updateOperatorInfo();
        } else {
            console.error('Setup Product - No current user returned');
            document.getElementById('current-operator').textContent = 'Error: No User';
        }
    } catch (error) {
        console.error('Error loading current user:', error);
        document.getElementById('current-operator').textContent = 'Error: User Load Failed';
    }
}

function updateOperatorInfo() {
    if (currentUser) {
        const operatorSpan = document.getElementById('current-operator');
        if (operatorSpan) {
            operatorSpan.textContent = currentUser.name || currentUser.username || 'Unknown';
        }

        // Update location name in header
        const locationNameElement = document.getElementById('location-name');
        if (locationNameElement) {
            if (currentUser.location_name) {
                locationNameElement.textContent = currentUser.location_name;
            } else {
                locationNameElement.textContent = 'Rainbow Station Inc.';
            }
        }
    }
}

// Time functions
function updateTime() {
    const now = new Date();
    document.getElementById('current-date').textContent = formatDate(now);
    document.getElementById('current-time').textContent = formatTime(now);
}

function formatDate(date) {
    return date.toLocaleDateString("en-US", { month: "2-digit", day: "2-digit", year: "2-digit" });
}

function formatTime(date) {
    return date.toLocaleTimeString("en-US", { hour12: false });
}

// Image Upload Functions
function initializeImageUpload() {
    const imageUploadArea = document.getElementById('imageUploadArea');
    const imageFileInput = document.getElementById('imageFileInput');
    const browseImageBtn = document.getElementById('browseImageBtn');
    const clearImageBtn = document.getElementById('clearImageBtn');
    const imagePreview = document.getElementById('imagePreview');
    const imageUploadText = document.getElementById('imageUploadText');
    const imageUploadIcon = document.getElementById('imageUploadIcon');

    // Browse button click
    browseImageBtn.addEventListener('click', () => {
        imageFileInput.click();
    });

    // Clear button click
    clearImageBtn.addEventListener('click', clearImage);

    // File input change
    imageFileInput.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
            handleImageFile(file);
        }
    });

    // Drag and drop events
    imageUploadArea.addEventListener('click', () => {
        imageFileInput.click();
    });

    imageUploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        imageUploadArea.classList.add('drag-over');
    });

    imageUploadArea.addEventListener('dragleave', (e) => {
        e.preventDefault();
        imageUploadArea.classList.remove('drag-over');
    });

    imageUploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        imageUploadArea.classList.remove('drag-over');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (file.type.startsWith('image/')) {
                handleImageFile(file);
            } else {
                showMessage('Please select a valid image file', 'error');
            }
        }
    });
}

function handleImageFile(file) {
    // Validate file type
    if (!file.type.startsWith('image/')) {
        showMessage('Please select a valid image file', 'error');
        return;
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
        showMessage('Image file size must be less than 5MB', 'error');
        return;
    }

    selectedImageFile = file;

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
        const imagePreview = document.getElementById('imagePreview');
        const imageUploadText = document.getElementById('imageUploadText');
        const imageUploadIcon = document.getElementById('imageUploadIcon');
        const imageConfirmCheckbox = document.getElementById('imageConfirm');

        imagePreview.src = e.target.result;
        imagePreview.style.display = 'block';
        imageUploadIcon.style.display = 'none';

        // Update text based on Image Confirm status
        if (imageConfirmCheckbox && imageConfirmCheckbox.checked) {
            imageUploadText.textContent = file.name;
        } else {
            imageUploadText.textContent = `${file.name} (will not be saved - check Image Confirm)`;
        }
    };
    reader.readAsDataURL(file);
}

function clearImage() {
    selectedImageFile = null;
    selectedImagePath = null;

    const imagePreview = document.getElementById('imagePreview');
    const imageUploadText = document.getElementById('imageUploadText');
    const imageUploadIcon = document.getElementById('imageUploadIcon');
    const imageFileInput = document.getElementById('imageFileInput');

    imagePreview.style.display = 'none';
    imagePreview.src = '';
    imageUploadText.textContent = 'Drag and drop an image here or click BROWSE';
    imageUploadIcon.style.display = 'block';
    imageFileInput.value = '';
}

function updateImageConfirmStatus() {
    const imageConfirmCheckbox = document.getElementById('imageConfirm');
    const imageUploadArea = document.getElementById('imageUploadArea');
    const imageUploadText = document.getElementById('imageUploadText');

    if (imageConfirmCheckbox && imageUploadArea) {
        if (imageConfirmCheckbox.checked) {
            // Image Confirm is checked - enable image saving
            imageUploadArea.style.opacity = '1';
            imageUploadArea.style.pointerEvents = 'auto';
            if (!selectedImageFile && !selectedImagePath) {
                imageUploadText.textContent = 'Drag and drop an image here or click BROWSE';
            }
        } else {
            // Image Confirm is not checked - indicate image won't be saved
            imageUploadArea.style.opacity = '0.6';
            imageUploadArea.style.pointerEvents = 'auto'; // Keep functional for preview
            if (imageUploadText.textContent.includes('Drag and drop') || imageUploadText.textContent.includes('Current image')) {
                imageUploadText.textContent = 'Image will not be saved (Image Confirm unchecked)';
            }
        }
    }
}

// Navigation functions
function navigateToAdmin() {
    ipcRenderer.invoke('navigate-to', 'admin');
}

// Window control functions
function closeApp() {
    ipcRenderer.invoke('close-app');
}

function minimizeApp() {
    ipcRenderer.invoke('minimize-app');
}

async function toggleFullscreen() {
    try {
        const isMaximized = await ipcRenderer.invoke('toggle-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error toggling maximize:', error);
    }
}

// Form functions
function getFormData() {
    return {
        barcode: document.getElementById('barcode').value,
        description: document.getElementById('description').value,
        category: document.getElementById('category').value,
        subCategory: document.getElementById('subCategory').value,
        supplier: document.getElementById('supplier').value,
        brandName: document.getElementById('brandName').value,
        purchasePrice: document.getElementById('purchasePrice').value,
        style: document.getElementById('style').value,
        color: document.getElementById('color').value,
        size: document.getElementById('size').value,
        minQty: document.getElementById('minQty').value,
        maxQty: document.getElementById('maxQty').value,
        specialDiscount: document.getElementById('specialDiscount').checked,
        priority: document.getElementById('priority').checked,
        imageConfirm: document.getElementById('imageConfirm').checked,
        nonScanable: document.getElementById('nonScanable').checked,
        deliItem: document.getElementById('deliItem').checked
    };
}

function setFormData(data) {
    document.getElementById('barcode').value = data.barcode || '';
    document.getElementById('description').value = data.description || '';
    document.getElementById('category').value = data.category || '';
    document.getElementById('subCategory').value = data.subCategory || '';
    document.getElementById('supplier').value = data.supplier || '';
    document.getElementById('brandName').value = data.brandName || '';
    document.getElementById('purchasePrice').value = data.purchasePrice || '';
    document.getElementById('style').value = data.style || '';
    document.getElementById('color').value = data.color || '';
    document.getElementById('size').value = data.size || '';
    document.getElementById('minQty').value = data.minQty || '';
    document.getElementById('maxQty').value = data.maxQty || '';
    document.getElementById('specialDiscount').checked = data.specialDiscount || false;
    document.getElementById('priority').checked = data.priority || false;
    document.getElementById('imageConfirm').checked = data.imageConfirm || false;
    document.getElementById('nonScanable').checked = data.nonScanable || false;
    document.getElementById('deliItem').checked = data.deliItem || false;
}

async function saveProduct() {
    const data = getFormData();

    if (!data.barcode || !data.description) {
        showMessage('Please fill in required fields: Barcode and Description', 'error');
        return;
    }

    // Security check: Ensure user has location access
    if (!currentUser || !currentUser.location_name) {
        showMessage('Error: No location assigned to current user', 'error');
        return;
    }

    // Get location stock data (only for current user's location)
    const stockData = [];
    const stockInputs = document.querySelectorAll('#locationStockTable input[data-type="stock"]');
    const priceInputs = document.querySelectorAll('#locationStockTable input[data-type="price"]');

    stockInputs.forEach((input, index) => {
        let stockValue = parseInt(input.value) || 0;

        // For new products (not in edit mode), if stock is 0 or not set, use max_qty as initial stock
        if (!isEditMode && stockValue === 0 && data.maxQty) {
            stockValue = parseInt(data.maxQty) || 0;
            console.log(`🔄 Setting initial stock to max_qty (${stockValue}) for location: ${locationStocks[index].location}`);
        }

        stockData.push({
            location: locationStocks[index].location,
            stock: stockValue,
            price: parseFloat(priceInputs[index].value) || 0.00
        });
    });

    console.log(`✅ SECURITY: Saving product to location: ${currentUser.location_name}`);
    console.log(`✅ SECURITY: Stock data:`, stockData);

    // Handle image upload - only save if Image Confirm checkbox is checked
    let imagePath = null; // Default to null (no image)

    if (data.imageConfirm) {
        // If Image Confirm is checked, keep existing image or save new one
        imagePath = selectedImagePath; // Keep existing image path if no new image

        if (selectedImageFile) {
            try {
                // Convert file to base64 for IPC transfer
                const reader = new FileReader();
                const fileData = await new Promise((resolve, reject) => {
                    reader.onload = () => resolve({
                        name: selectedImageFile.name,
                        data: reader.result
                    });
                    reader.onerror = reject;
                    reader.readAsDataURL(selectedImageFile);
                });

                const imageResult = await ipcRenderer.invoke('save-product-image', fileData, data.barcode);
                if (imageResult.success) {
                    imagePath = imageResult.imagePath;
                    selectedImagePath = imagePath; // Update the selected path
                    selectedImageFile = null; // Clear the file since it's now saved
                } else {
                    showMessage('Error saving image: ' + imageResult.message, 'error');
                    return;
                }
            } catch (error) {
                console.error('Error saving image:', error);
                showMessage('Error saving image: ' + error.message, 'error');
                return;
            }
        }
    } else {
        // If Image Confirm is not checked, don't save any image (set to null)
        imagePath = null;
        console.log('Image Confirm not checked - image will not be saved to database');
    }

    console.log('Final image path for database:', imagePath);

    // Prepare product data for database
    const productData = {
        barcode: data.barcode,
        description: data.description,
        category: data.category,
        subcategory: data.subCategory,
        supplier: data.supplier,
        brand_name: data.brandName,
        purchase_price: parseFloat(data.purchasePrice) || 0,
        style: data.style,
        color: data.color,
        size: data.size,
        min_qty: parseInt(data.minQty) || 0,
        max_qty: parseInt(data.maxQty) || 0,
        image_path: imagePath,
        special_discount: data.specialDiscount,
        priority: data.priority,
        image_confirm: data.imageConfirm,
        non_scanable: data.nonScanable,
        daily_item: data.deliItem
    };

    try {
        let result;
        if (isEditMode && editingProductId) {
            // Update existing product
            result = await ipcRenderer.invoke('update-product', editingProductId, productData, stockData);
            if (result.success) {
                showMessage('Product updated successfully!', 'success');
                exitEditMode();
            } else {
                showMessage('Error updating product: ' + result.message, 'error');
            }
        } else {
            // Check if product already exists for new creation
            const existingProduct = await ipcRenderer.invoke('get-product-by-barcode', data.barcode);
            if (existingProduct.success && existingProduct.product) {
                showMessage('Product with this barcode already exists!', 'error');
                return;
            }

            // Create new product
            result = await ipcRenderer.invoke('create-product', productData, stockData);
            if (result.success) {
                showMessage('Product created successfully!', 'success');
            } else {
                showMessage('Error creating product: ' + result.message, 'error');
            }
        }

        if (result.success) {
            await loadProducts(); // Reload products table
            clearForm();
        }

        console.log('Product operation result:', result);
    } catch (error) {
        console.error('Error saving product:', error);
        showMessage('Error saving product: ' + error.message, 'error');
    }
}

// Exit edit mode
function exitEditMode() {
    isEditMode = false;
    editingProductId = null;
    const saveBtn = document.querySelector('.btn-green');
    if (saveBtn) {
        saveBtn.textContent = 'SAVE';
        saveBtn.style.backgroundColor = '#10b981';
    }
}

function clearForm() {
    setFormData({});
    exitEditMode(); // Exit edit mode when clearing form

    // Clear location stock table
    const stockInputs = document.querySelectorAll('#locationStockTable input[data-type="stock"]');
    const priceInputs = document.querySelectorAll('#locationStockTable input[data-type="price"]');

    stockInputs.forEach(input => input.value = '0');
    priceInputs.forEach(input => input.value = '0.00');

    // Clear image
    clearImage();

    // Update image confirm status
    updateImageConfirmStatus();

    console.log('Form cleared');
}

async function searchProduct() {
    const barcode = document.getElementById('barcode').value;

    if (!barcode) {
        alert('Please enter a barcode to search');
        return;
    }

    try {
        const result = await ipcRenderer.invoke('get-product-by-barcode', barcode);

        if (result.success && result.product) {
            const product = result.product;
            const locationStocks = result.locationStocks || [];

            // Populate form with product data
            setFormData({
                barcode: product.barcode,
                description: product.description,
                category: product.category,
                subCategory: product.subcategory,
                supplier: product.supplier,
                purchasePrice: product.purchase_price,
                style: product.style,
                color: product.color,
                size: product.size,
                minQty: product.min_qty,
                maxQty: product.max_qty,
                specialDiscount: product.special_discount === 1,
                priority: product.priority === 1,
                imageConfirm: product.image_confirm === 1,
                nonScanable: product.non_scanable === 1,
                deliItem: product.daily_item === 1
            });

            // Populate location stock table
            const stockInputs = document.querySelectorAll('#locationStockTable input[data-type="stock"]');
            const priceInputs = document.querySelectorAll('#locationStockTable input[data-type="price"]');

            stockInputs.forEach((input, index) => {
                const locationStock = locationStocks.find(ls => ls.location === locationStocks[index]?.location);
                input.value = locationStock ? locationStock.stock : 0;
            });

            priceInputs.forEach((input, index) => {
                const locationStock = locationStocks.find(ls => ls.location === locationStocks[index]?.location);
                input.value = locationStock ? locationStock.price.toFixed(2) : '0.00';
            });

            alert('Product loaded successfully!');
        } else {
            alert('Product not found with barcode: ' + barcode);
        }
    } catch (error) {
        console.error('Error searching product:', error);
        alert('Error searching product: ' + error.message);
    }
}

// Load data from database functions
async function loadLocations() {
    try {
        // Only load current user's location for product setup
        if (!currentUser || !currentUser.location_name) {
            console.error('No current user or location for product setup');
            return;
        }

        // Create location stock entry only for current user's location
        locationStocks = [{
            location: currentUser.location_name,
            stock: 0,
            price: 0.00
        }];

        console.log(`Setup Product - Loaded current user's location: ${currentUser.location_name}`);
        console.log('✅ SECURITY: Product will be saved only to current user\'s location');
    } catch (error) {
        console.error('Error loading locations:', error);
    }
}

async function loadCategories() {
    try {
        const result = await ipcRenderer.invoke('get-all-categories');
        if (result.success) {
            categories = result.categories;
            populateCategoryDropdown();
        } else {
            console.error('Error loading categories:', result.message);
        }
    } catch (error) {
        console.error('Error loading categories:', error);
    }
}

async function loadSuppliers() {
    try {
        const result = await ipcRenderer.invoke('get-all-suppliers');
        if (result.success) {
            suppliers = result.suppliers;
            populateSupplierDropdown();
        } else {
            console.error('Error loading suppliers:', result.message);
        }
    } catch (error) {
        console.error('Error loading suppliers:', error);
    }
}

function populateCategoryDropdown() {
    const categorySelect = document.getElementById('category');
    const subCategorySelect = document.getElementById('subCategory');

    if (categorySelect) {
        categorySelect.innerHTML = '<option value="">Select Category</option>';

        // Get parent categories (those without parent_id)
        const parentCategories = categories.filter(cat => !cat.parent_id);

        parentCategories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.name;
            option.textContent = category.name;
            categorySelect.appendChild(option);
        });

        // Add event listener for category change to populate subcategories
        categorySelect.addEventListener('change', function() {
            populateSubCategoryDropdown(this.value);
        });
    }
}

function populateSubCategoryDropdown(selectedCategory) {
    const subCategorySelect = document.getElementById('subCategory');

    if (subCategorySelect) {
        subCategorySelect.innerHTML = '<option value="">Select Subcategory</option>';

        if (selectedCategory) {
            // Find the parent category
            const parentCategory = categories.find(cat => cat.name === selectedCategory && !cat.parent_id);

            if (parentCategory) {
                // Get subcategories for this parent
                const subcategories = categories.filter(cat => cat.parent_id === parentCategory.id);

                subcategories.forEach(subcategory => {
                    const option = document.createElement('option');
                    option.value = subcategory.name;
                    option.textContent = subcategory.name;
                    subCategorySelect.appendChild(option);
                });
            }
        }
    }
}

function populateSupplierDropdown() {
    const supplierSelect = document.getElementById('supplier');

    if (supplierSelect) {
        supplierSelect.innerHTML = '<option value="">Select Supplier</option>';

        suppliers.forEach(supplier => {
            const option = document.createElement('option');
            option.value = supplier.name;
            option.textContent = supplier.name;
            supplierSelect.appendChild(option);
        });
    }
}

async function deleteProduct() {
    const barcode = document.getElementById('barcode').value;

    if (!barcode) {
        alert('Please enter a barcode to delete');
        return;
    }

    try {
        // First find the product by barcode
        const existingProduct = await ipcRenderer.invoke('get-product-by-barcode', barcode);

        if (!existingProduct.success || !existingProduct.product) {
            alert('Product not found with barcode: ' + barcode);
            return;
        }

        if (confirm('Are you sure you want to delete this product?\n\nBarcode: ' + barcode + '\nDescription: ' + existingProduct.product.description)) {
            const result = await ipcRenderer.invoke('delete-product', existingProduct.product.id);

            if (result.success) {
                clearForm();
                alert('Product deleted successfully!');
            } else {
                alert('Error deleting product: ' + result.message);
            }
        }
    } catch (error) {
        console.error('Error deleting product:', error);
        alert('Error deleting product: ' + error.message);
    }
}

function renderLocationStockTable() {
    const tbody = document.getElementById('locationStockTable');
    tbody.innerHTML = '';

    locationStocks.forEach((location, index) => {
        const row = document.createElement('tr');
        
        row.innerHTML = `
            <td class="font-medium">${location.location}</td>
            <td class="text-center">
                <input type="number" class="form-input" data-type="stock" value="${location.stock}" placeholder="0">
            </td>
            <td class="text-center">
                <input type="number" step="0.01" class="form-input price" data-type="price" value="${location.price.toFixed(2)}" placeholder="0.00">
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// Update location stock when inputs change
document.addEventListener('input', function(e) {
    if (e.target.dataset.type === 'stock') {
        const index = Array.from(document.querySelectorAll('input[data-type="stock"]')).indexOf(e.target);
        if (index >= 0) {
            locationStocks[index].stock = parseInt(e.target.value) || 0;
        }
    } else if (e.target.dataset.type === 'price') {
        const index = Array.from(document.querySelectorAll('input[data-type="price"]')).indexOf(e.target);
        if (index >= 0) {
            locationStocks[index].price = parseFloat(e.target.value) || 0.00;
        }
    }
});

// Product management functions
let products = [];

async function loadProducts() {
    try {
        const result = await ipcRenderer.invoke('get-all-products');

        if (result.success) {
            products = result.products.map(product => ({
                id: product.id,
                barcode: product.barcode,
                description: product.description,
                category: product.category,
                subcategory: product.subcategory,
                supplier: product.supplier,
                purchasePrice: product.purchase_price,
                fullData: product
            }));

            renderProductTable();
        } else {
            console.error('Error loading products:', result.message);
        }
    } catch (error) {
        console.error('Error loading products:', error);
    }
}

function renderProductTable() {
    const tbody = document.getElementById('productTableBody');
    if (!tbody) {
        console.error('productTableBody element not found!');
        return;
    }

    tbody.innerHTML = '';

    products.forEach((product, index) => {
        const row = document.createElement('tr');
        row.style.cursor = 'pointer';

        row.innerHTML = `
            <td class="font-medium">${product.barcode}</td>
            <td>${product.description}</td>
            <td class="text-center">${product.category || ''}</td>
            <td class="text-center">${product.subcategory || ''}</td>
            <td class="text-center">${product.supplier || ''}</td>
            <td class="text-center">$${parseFloat(product.purchasePrice || 0).toFixed(2)}</td>
            <td class="text-center">
                <div class="flex gap-2 justify-center">
                    <button class="btn btn-blue btn-small" onclick="editProduct(${index})" title="Edit Product">
                        Edit
                    </button>
                    <button class="btn btn-red btn-small" onclick="confirmDeleteProduct(${index})" title="Delete Product">
                        Delete
                    </button>
                </div>
            </td>
        `;

        tbody.appendChild(row);
    });
}

// Edit product function
function editProduct(index) {
    const product = products[index];
    if (product && product.fullData) {
        // Set edit mode
        isEditMode = true;
        editingProductId = product.fullData.id;

        // Load product data into form
        setFormData({
            barcode: product.fullData.barcode,
            description: product.fullData.description,
            category: product.fullData.category || '',
            subCategory: product.fullData.subcategory || '',
            supplier: product.fullData.supplier || '',
            brandName: product.fullData.brand_name || '',
            purchasePrice: product.fullData.purchase_price || '',
            style: product.fullData.style || '',
            color: product.fullData.color || '',
            size: product.fullData.size || '',
            minQty: product.fullData.min_qty || '',
            maxQty: product.fullData.max_qty || '',
            specialDiscount: product.fullData.special_discount || false,
            priority: product.fullData.priority || false,
            imageConfirm: product.fullData.image_confirm || false,
            nonScanable: product.fullData.non_scanable || false,
            deliItem: product.fullData.daily_item || false
        });

        // Load existing image if available
        if (product.fullData.image_path) {
            selectedImagePath = product.fullData.image_path;
            selectedImageFile = null; // Clear any new file selection

            const imagePreview = document.getElementById('imagePreview');
            const imageUploadText = document.getElementById('imageUploadText');
            const imageUploadIcon = document.getElementById('imageUploadIcon');

            // Show existing image - convert path to file URL
            const imagePath = product.fullData.image_path.replace(/\\/g, '/');
            imagePreview.src = `file:///${imagePath}`;
            imagePreview.style.display = 'block';
            imageUploadText.textContent = 'Current image loaded';
            imageUploadIcon.style.display = 'none';
        } else {
            clearImage();
        }

        // Update image confirm status after loading data
        updateImageConfirmStatus();

        // Change save button to update mode
        const saveBtn = document.querySelector('.btn-green');
        if (saveBtn) {
            saveBtn.textContent = 'UPDATE';
            saveBtn.style.backgroundColor = '#f59e0b';
        }

        showMessage(`Editing product: ${product.fullData.description}`, 'info');

        // Scroll to form
        document.querySelector('.form-card').scrollIntoView({ behavior: 'smooth' });
    }
}

// Confirm delete function
function confirmDeleteProduct(index) {
    const product = products[index];
    if (product && product.fullData && confirm(`Are you sure you want to delete product "${product.fullData.description}"?`)) {
        deleteProductById(product.fullData.id);
    }
}

// Delete product by ID
async function deleteProductById(productId) {
    try {
        const result = await ipcRenderer.invoke('delete-product', productId);

        if (result.success) {
            showMessage('Product deleted successfully!', 'success');
            await loadProducts();
            clearForm();
        } else {
            showMessage('Error deleting product: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error deleting product:', error);
        showMessage('Error deleting product: ' + error.message, 'error');
    }
}
