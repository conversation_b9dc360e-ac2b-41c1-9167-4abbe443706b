const { SupabaseSync } = require('./src/supabase-config');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

async function testFullSync() {
    console.log('🧪 Testing Full Sync to Verify Fix\n');
    
    try {
        // Create a mock database object that points to the actual database file
        const dbPath = path.join(__dirname, 'src', 'pos_system.db');
        console.log('📁 Using database:', dbPath);
        
        const mockDatabase = {
            db: new sqlite3.Database(dbPath)
        };
        
        const supabaseSync = new SupabaseSync(mockDatabase);
        
        console.log('🔄 Running full sync to test the fix...\n');
        
        // Run full sync
        const result = await supabaseSync.syncAllData();
        
        console.log('\n📊 Full Sync Result:');
        console.log('====================');
        console.log(`Status: ${result.success ? '✅ SUCCESS' : '❌ FAILED'}`);
        console.log(`Message: ${result.message}`);
        
        if (!result.success) {
            console.log(`Error: ${result.error}`);
        } else {
            console.log('\n🎯 SUCCESS! The foreign key constraint violation has been fixed.');
            console.log('All tables synced successfully without foreign key errors.');
        }
        
    } catch (error) {
        console.error('❌ Test failed with error:', error);
        console.error('Stack trace:', error.stack);
    }
}

// Run the test
testFullSync().then(() => {
    console.log('\n🏁 Test completed.');
    process.exit(0);
}).catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
});
