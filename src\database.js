const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');
const path = require('path');
const { app } = require('electron');
const fs = require('fs');

class Database {
  constructor() {
    this.db = null;
  }

  init() {
    let dbPath;
    let dbDirectory;

    // Environment-based database path selection
    if (app.isPackaged) {
      // Production: Use user data directory (safe, writable location)
      const userDataPath = app.getPath('userData');
      dbPath = path.join(userDataPath, 'pos_system.db');
      dbDirectory = userDataPath;
      console.log('🏭 Production mode: Using user data directory');
      console.log('📁 User Data Path:', userDataPath);
    } else {
      // Development: Use local src directory for testing
      dbPath = path.join(__dirname, 'pos_system.db');
      dbDirectory = __dirname;
      console.log('🔧 Development mode: Using local src directory');
      console.log('📁 Project Src Path:', __dirname);
    }

    // Ensure the database directory exists
    if (!fs.existsSync(dbDirectory)) {
      fs.mkdirSync(dbDirectory, { recursive: true });
      console.log('📂 Created database directory:', dbDirectory);
    }

    console.log('🗄️  Database path:', dbPath);

    this.db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('Error opening database:', err);
      } else {
        console.log('Connected to SQLite database');
        this.createTables();
        this.createDefaultAdmin();
      }
    });
  }

  createTables() {
    const createUserTable = `
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        role TEXT NOT NULL,
        name TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_login DATETIME,
        status TEXT DEFAULT 'active'
      )
    `;

    const createPermissionsTable = `
      CREATE TABLE IF NOT EXISTS user_permissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        module_id TEXT NOT NULL,
        module_name TEXT NOT NULL,
        can_view INTEGER DEFAULT 0,
        can_edit INTEGER DEFAULT 0,
        can_delete INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        UNIQUE(user_id, module_id)
      )
    `;

    const createProductsTable = `
      CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        barcode TEXT UNIQUE NOT NULL,
        description TEXT NOT NULL,
        category TEXT,
        subcategory TEXT,
        supplier TEXT,
        brand_name TEXT,
        purchase_price DECIMAL(10,2),
        style TEXT,
        color TEXT,
        size TEXT,
        min_qty INTEGER DEFAULT 0,
        max_qty INTEGER DEFAULT 0,
        image_path TEXT,
        special_discount INTEGER DEFAULT 0,
        priority INTEGER DEFAULT 0,
        image_confirm INTEGER DEFAULT 0,
        non_scanable INTEGER DEFAULT 0,
        daily_item INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    const createLocationStocksTable = `
      CREATE TABLE IF NOT EXISTS location_stocks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        location TEXT NOT NULL,
        stock INTEGER DEFAULT 0,
        min_qty INTEGER DEFAULT 0,
        price DECIMAL(10,2) DEFAULT 0.00,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
        UNIQUE(product_id, location)
      )
    `;

    const createCategoriesTable = `
      CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        parent_id INTEGER,
        description TEXT,
        display_order INTEGER DEFAULT 0,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES categories (id) ON DELETE SET NULL
      )
    `;

    const createSuppliersTable = `
      CREATE TABLE IF NOT EXISTS suppliers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        address1 TEXT,
        address2 TEXT,
        city TEXT,
        state TEXT,
        zip_code TEXT,
        telephone TEXT,
        fax TEXT,
        email TEXT,
        sales_rep TEXT,
        sales_rep_phone TEXT,
        retail_website TEXT,
        wholesale_website TEXT,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    const createLocationsTable = `
      CREATE TABLE IF NOT EXISTS locations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        location_code TEXT UNIQUE NOT NULL,
        location TEXT NOT NULL,
        company_name TEXT,
        address1 TEXT,
        address2 TEXT,
        phone TEXT,
        tax_percent DECIMAL(5,3) DEFAULT 0.000,
        email TEXT,
        app_mode TEXT,
        theater_plu TEXT,
        theater_time TEXT,
        theater_ticket_price DECIMAL(10,2) DEFAULT 56.00,
        deli INTEGER DEFAULT 0,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    const createTicketsTable = `
      CREATE TABLE IF NOT EXISTS tickets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        ticket_id TEXT UNIQUE NOT NULL,
        duration INTEGER NOT NULL,
        photo_path TEXT,
        payment_method TEXT NOT NULL,
        ticket_price DECIMAL(10,2) DEFAULT 56.00,
        total_amount DECIMAL(10,2) DEFAULT 56.00,
        user_id INTEGER,
        location_id INTEGER,
        operator_name TEXT,
        location_name TEXT,
        issued_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        status TEXT DEFAULT 'active',
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL,
        FOREIGN KEY (location_id) REFERENCES locations (id) ON DELETE SET NULL
      )
    `;

    const createBannedTicketsTable = `
      CREATE TABLE IF NOT EXISTS banned_tickets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        original_ticket_id INTEGER NOT NULL,
        ticket_id TEXT NOT NULL,
        duration INTEGER NOT NULL,
        photo_path TEXT,
        payment_method TEXT NOT NULL,
        user_id INTEGER,
        location_id INTEGER,
        operator_name TEXT,
        location_name TEXT,
        issued_at DATETIME NOT NULL,
        banned_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        banned_by_user_id INTEGER,
        banned_by_operator TEXT,
        ban_reason TEXT NOT NULL,
        action_type TEXT NOT NULL CHECK (action_type IN ('ban', 'grant')),
        is_refunded BOOLEAN DEFAULT 0,
        refund_amount DECIMAL(10,2),
        refunded_at DATETIME,
        refunded_by_user_id INTEGER,
        refunded_by_operator TEXT
      )
    `;

    const createSalesTable = `
      CREATE TABLE IF NOT EXISTS sales (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sale_id TEXT UNIQUE NOT NULL,
        user_id INTEGER,
        location_id INTEGER,
        operator_name TEXT,
        location_name TEXT,
        subtotal DECIMAL(10,2) NOT NULL,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        total_amount DECIMAL(10,2) NOT NULL,
        payment_cash DECIMAL(10,2) DEFAULT 0,
        payment_debit DECIMAL(10,2) DEFAULT 0,
        payment_credit DECIMAL(10,2) DEFAULT 0,
        payment_total DECIMAL(10,2) NOT NULL,
        change_amount DECIMAL(10,2) DEFAULT 0,
        item_count INTEGER NOT NULL,
        sale_type TEXT DEFAULT 'sale',
        sale_date DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        status TEXT DEFAULT 'completed',
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL,
        FOREIGN KEY (location_id) REFERENCES locations (id) ON DELETE SET NULL
      )
    `;

    const createSalesItemsTable = `
      CREATE TABLE IF NOT EXISTS sales_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sale_id TEXT NOT NULL,
        product_id INTEGER,
        product_name TEXT NOT NULL,
        product_barcode TEXT,
        product_category TEXT,
        product_subcategory TEXT,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        FOREIGN KEY (sale_id) REFERENCES sales (sale_id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE SET NULL
      )
    `;

    const createDraftSalesTable = `
      CREATE TABLE IF NOT EXISTS draft_sales (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        draft_sale_id TEXT UNIQUE NOT NULL,
        user_id INTEGER,
        location_id INTEGER,
        operator_name TEXT,
        location_name TEXT,
        item_count INTEGER NOT NULL,
        total_amount DECIMAL(10,2) NOT NULL,
        created_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        status TEXT DEFAULT 'draft',
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL,
        FOREIGN KEY (location_id) REFERENCES locations (id) ON DELETE SET NULL
      )
    `;

    const createDraftSalesItemsTable = `
      CREATE TABLE IF NOT EXISTS draft_sales_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        draft_sale_id TEXT NOT NULL,
        product_id INTEGER,
        product_name TEXT NOT NULL,
        product_barcode TEXT,
        product_category TEXT,
        product_subcategory TEXT,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        discount DECIMAL(10,2) DEFAULT 0,
        FOREIGN KEY (draft_sale_id) REFERENCES draft_sales (draft_sale_id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE SET NULL
      )
    `;

    const createShiftsTable = `
      CREATE TABLE IF NOT EXISTS shifts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        shift_id TEXT UNIQUE NOT NULL,
        user_id INTEGER NOT NULL,
        location_id INTEGER NOT NULL,
        operator_name TEXT NOT NULL,
        location_name TEXT NOT NULL,
        shift_start_time DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        shift_end_time DATETIME NOT NULL,
        actual_end_time DATETIME,
        shift_duration_hours INTEGER DEFAULT 12,
        remaining_time_minutes INTEGER,
        status TEXT DEFAULT 'active' CHECK (status IN ('active', 'ended', 'expired')),
        total_sales DECIMAL(10,2) DEFAULT 0,
        total_transactions INTEGER DEFAULT 0,
        shift_type TEXT CHECK (shift_type IN ('day', 'night')),
        scheduled_start_time DATETIME,
        scheduled_end_time DATETIME,
        overtime_minutes INTEGER DEFAULT 0,
        carryover_minutes INTEGER DEFAULT 0,
        previous_shift_id TEXT,
        login_time DATETIME,
        created_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        updated_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        FOREIGN KEY (location_id) REFERENCES locations (id) ON DELETE CASCADE
      )
    `;

    this.db.run(createUserTable, (err) => {
      if (err) {
        console.error('Error creating users table:', err);
      } else {
        console.log('Users table created successfully');
        // Add name column if it doesn't exist (for existing databases)
        this.addNameColumnIfNotExists();
      }
    });

    this.db.run(createPermissionsTable, (err) => {
      if (err) {
        console.error('Error creating permissions table:', err);
      } else {
        console.log('Permissions table created successfully');
        // Verify table creation
        this.verifyPermissionsTable();
      }
    });

    this.db.run(createProductsTable, (err) => {
      if (err) {
        console.error('Error creating products table:', err);
      } else {
        console.log('Products table created successfully');
      }
    });

    this.db.run(createLocationStocksTable, (err) => {
      if (err) {
        console.error('Error creating location_stocks table:', err);
      } else {
        console.log('Location stocks table created successfully');
        // Add min_qty column if it doesn't exist (for existing databases)
        this.addMinQtyColumnToLocationStocks().catch(err => {
          console.error('Error adding min_qty column to location_stocks:', err);
        });
      }
    });

    this.db.run(createCategoriesTable, (err) => {
      if (err) {
        console.error('Error creating categories table:', err);
      } else {
        console.log('Categories table created successfully');
      }
    });

    this.db.run(createSuppliersTable, (err) => {
      if (err) {
        console.error('Error creating suppliers table:', err);
      } else {
        console.log('Suppliers table created successfully');
      }
    });

    this.db.run(createLocationsTable, (err) => {
      if (err) {
        console.error('Error creating locations table:', err);
      } else {
        console.log('Locations table created successfully');
      }
    });

    this.db.run(createTicketsTable, (err) => {
      if (err) {
        console.error('Error creating tickets table:', err);
      } else {
        console.log('Tickets table created successfully');
        // Add price columns if they don't exist (for existing databases)
        this.addTicketPriceColumns().catch(err => {
          console.error('Error adding ticket price columns:', err);
        });
      }
    });

    this.db.run(createBannedTicketsTable, (err) => {
      if (err) {
        console.error('Error creating banned_tickets table:', err);
      } else {
        console.log('Banned tickets table created successfully');
      }
    });

    this.db.run(createSalesTable, (err) => {
      if (err) {
        console.error('Error creating sales table:', err);
      } else {
        console.log('Sales table created successfully');
        // Add sale_type column if it doesn't exist (for existing databases)
        this.addSaleTypeColumn().catch(err => {
          console.error('Error adding sale_type column:', err);
        });
      }
    });

    this.db.run(createSalesItemsTable, (err) => {
      if (err) {
        console.error('Error creating sales_items table:', err);
      } else {
        console.log('Sales items table created successfully');
      }
    });

    this.db.run(createDraftSalesTable, (err) => {
      if (err) {
        console.error('Error creating draft_sales table:', err);
      } else {
        console.log('Draft sales table created successfully');
      }
    });

    this.db.run(createDraftSalesItemsTable, (err) => {
      if (err) {
        console.error('Error creating draft_sales_items table:', err);
      } else {
        console.log('Draft sales items table created successfully');
      }
    });

    this.db.run(createShiftsTable, (err) => {
      if (err) {
        console.error('Error creating shifts table:', err);
      } else {
        console.log('Shifts table created successfully');
        // Add sales type columns if they don't exist (for existing databases)
        this.addShiftSalesTypeColumns().catch(err => {
          console.error('Error adding shift sales type columns:', err);
        });
        // Add new shift management columns if they don't exist (for existing databases)
        this.addShiftManagementColumns().catch(err => {
          console.error('Error adding shift management columns:', err);
        });
      }
    });

    // Add status column to tickets table if it doesn't exist
    this.addStatusColumnIfNotExists();
  }

  addStatusColumnIfNotExists() {
    // Check if status column exists in tickets table, if not add it
    this.db.all("PRAGMA table_info(tickets)", (err, columns) => {
      if (err) {
        console.error('Error checking tickets table structure:', err);
        return;
      }

      const hasStatusColumn = columns.some(col => col.name === 'status');

      if (!hasStatusColumn) {
        this.db.run("ALTER TABLE tickets ADD COLUMN status TEXT DEFAULT 'active'", (err) => {
          if (err) {
            console.error('Error adding status column to tickets table:', err);
          } else {
            console.log('Status column added to tickets table successfully');
            // Update existing tickets to have 'active' status
            this.db.run("UPDATE tickets SET status = 'active' WHERE status IS NULL", (updateErr) => {
              if (updateErr) {
                console.error('Error updating existing tickets with active status:', updateErr);
              } else {
                console.log('Updated existing tickets with active status');
              }
            });
          }
        });
      }
    });
  }

  addNameColumnIfNotExists() {
    // Check if name column exists, if not add it
    this.db.all("PRAGMA table_info(users)", (err, columns) => {
      if (err) {
        console.error('Error checking table structure:', err);
        return;
      }

      const hasNameColumn = columns.some(col => col.name === 'name');
      const hasLastLoginColumn = columns.some(col => col.name === 'last_login');
      const hasStatusColumn = columns.some(col => col.name === 'status');
      const hasLocationIdColumn = columns.some(col => col.name === 'location_id');

      if (!hasNameColumn) {
        this.db.run("ALTER TABLE users ADD COLUMN name TEXT", (err) => {
          if (err) {
            console.error('Error adding name column:', err);
          } else {
            console.log('Name column added successfully');
          }
        });
      }

      if (!hasLastLoginColumn) {
        this.db.run("ALTER TABLE users ADD COLUMN last_login DATETIME", (err) => {
          if (err) {
            console.error('Error adding last_login column:', err);
          } else {
            console.log('Last_login column added successfully');
          }
        });
      }

      if (!hasStatusColumn) {
        this.db.run("ALTER TABLE users ADD COLUMN status TEXT DEFAULT 'active'", (err) => {
          if (err) {
            console.error('Error adding status column:', err);
          } else {
            console.log('Status column added successfully');
          }
        });
      }

      if (!hasLocationIdColumn) {
        this.db.run("ALTER TABLE users ADD COLUMN location_id INTEGER", (err) => {
          if (err) {
            console.error('Error adding location_id column:', err);
          } else {
            console.log('Location ID column added successfully');
          }
        });
      }
    });
  }

  verifyPermissionsTable() {
    // Check if permissions table exists and log its structure
    this.db.all("PRAGMA table_info(user_permissions)", (err, columns) => {
      if (err) {
        console.error('Error checking permissions table structure:', err);
      } else if (columns.length > 0) {
        console.log('Permissions table structure verified:', columns.map(col => col.name));
      } else {
        console.log('Permissions table not found, attempting to create...');
      }
    });

    // Also check if table exists in sqlite_master
    this.db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='user_permissions'", (err, row) => {
      if (err) {
        console.error('Error checking table existence:', err);
      } else if (row) {
        console.log('Permissions table exists in database');
      } else {
        console.log('Permissions table does not exist in database');
      }
    });
  }

  async addMinQtyColumnToLocationStocks() {
    return new Promise((resolve, reject) => {
      // Check if min_qty column exists in location_stocks table
      this.db.all("PRAGMA table_info(location_stocks)", (err, columns) => {
        if (err) {
          console.error('Error checking location_stocks table structure:', err);
          reject(err);
          return;
        }

        const hasMinQtyColumn = columns.some(col => col.name === 'min_qty');

        if (!hasMinQtyColumn) {
          this.db.run("ALTER TABLE location_stocks ADD COLUMN min_qty INTEGER DEFAULT 0", (err) => {
            if (err) {
              console.error('Error adding min_qty column to location_stocks table:', err);
              reject(err);
            } else {
              console.log('min_qty column added to location_stocks table successfully');

              // Migrate existing data: copy min_qty from products table to location_stocks
              const migrateQuery = `
                UPDATE location_stocks
                SET min_qty = (
                  SELECT p.min_qty
                  FROM products p
                  WHERE p.id = location_stocks.product_id
                )
                WHERE min_qty = 0
              `;

              this.db.run(migrateQuery, (migrateErr) => {
                if (migrateErr) {
                  console.error('Error migrating min_qty data:', migrateErr);
                  reject(migrateErr);
                } else {
                  console.log('Successfully migrated min_qty data from products to location_stocks');
                  resolve();
                }
              });
            }
          });
        } else {
          console.log('min_qty column already exists in location_stocks table');
          resolve();
        }
      });
    });
  }

  async createDefaultAdmin() {
    // Create default admin user
    const defaultUsername = 'admin';
    const defaultPassword = 'admin123';
    const defaultName = 'System Administrator';
    const hashedPassword = await bcrypt.hash(defaultPassword, 10);

    const insertAdmin = `
      INSERT OR IGNORE INTO users (username, password, role, name, status)
      VALUES (?, ?, 'Admin', ?, 'active')
    `;

    this.db.run(insertAdmin, [defaultUsername, hashedPassword, defaultName], function(err) {
      if (err) {
        console.error('Error creating default admin:', err);
      } else {
        console.log('Default admin user created (username: admin, password: admin123)');
      }
    });

    // Create default cashier user for testing
    const cashierPassword = await bcrypt.hash('cashier123', 10);
    const insertCashier = `
      INSERT OR IGNORE INTO users (username, password, role, name, status)
      VALUES (?, ?, 'Cashier', ?, 'active')
    `;

    this.db.run(insertCashier, ['cashier', cashierPassword, 'Test Cashier'], function(err) {
      if (err) {
        console.error('Error creating default cashier:', err);
      } else {
        console.log('Default cashier user created (username: cashier, password: cashier123)');
      }
    });

    // Create default CCTV user for testing
    const cctvPassword = await bcrypt.hash('cctv123', 10);
    const insertCCTV = `
      INSERT OR IGNORE INTO users (username, password, role, name, status)
      VALUES (?, ?, 'CCTV', ?, 'active')
    `;

    this.db.run(insertCCTV, ['cctv', cctvPassword, 'Test CCTV User'], function(err) {
      if (err) {
        console.error('Error creating default CCTV user:', err);
      } else {
        console.log('Default CCTV user created (username: cctv, password: cctv123)');
      }
    });
  }

  authenticateUser(username, password, role) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT u.*, l.location as location_name, l.location_code, l.company_name, l.theater_time
        FROM users u
        LEFT JOIN locations l ON u.location_id = l.id
        WHERE u.username = ? AND u.role = ? AND u.status = 'active'
      `;

      this.db.get(query, [username, role], async (err, user) => {
        if (err) {
          reject(err);
        } else if (user) {
          // Verify password
          const isValidPassword = await bcrypt.compare(password, user.password);
          if (isValidPassword) {
            // Update last login
            this.updateLastLogin(user.id);
            resolve(user);
          } else {
            resolve(null);
          }
        } else {
          resolve(null);
        }
      });
    });
  }

  updateLastLogin(userId) {
    const query = `UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?`;
    this.db.run(query, [userId], (err) => {
      if (err) {
        console.error('Error updating last login:', err);
      }
    });
  }

  // User Management Functions
  async createUser(userData) {
    const { username, password, role, name, location_id } = userData;
    const hashedPassword = await bcrypt.hash(password, 10);

    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO users (username, password, role, name, location_id, status)
        VALUES (?, ?, ?, ?, ?, 'active')
      `;

      this.db.run(query, [username, hashedPassword, role, name, location_id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, username, role, name, location_id, status: 'active' });
        }
      });
    });
  }

  getAllUsers() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT u.id, u.username, u.role, u.name, u.status, u.created_at, u.last_login,
               u.location_id, l.location as location_name, l.location_code
        FROM users u
        LEFT JOIN locations l ON u.location_id = l.id
        ORDER BY u.created_at DESC
      `;

      this.db.all(query, [], (err, users) => {
        if (err) {
          reject(err);
        } else {
          resolve(users);
        }
      });
    });
  }

  getUserById(id) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT u.id, u.username, u.role, u.name, u.status, u.created_at, u.last_login,
               u.location_id, l.location as location_name, l.location_code
        FROM users u
        LEFT JOIN locations l ON u.location_id = l.id
        WHERE u.id = ?
      `;

      this.db.get(query, [id], (err, user) => {
        if (err) {
          reject(err);
        } else {
          resolve(user);
        }
      });
    });
  }

  getUserWithLocationById(id) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT u.id, u.username, u.role, u.name, u.status, u.created_at, u.last_login,
               u.location_id, l.location as location_name, l.location_code,
               l.theater_time, l.company_name, l.address1, l.address2, l.phone, l.tax_percent
        FROM users u
        LEFT JOIN locations l ON u.location_id = l.id
        WHERE u.id = ?
      `;

      this.db.get(query, [id], (err, user) => {
        if (err) {
          reject(err);
        } else {
          resolve(user);
        }
      });
    });
  }

  async updateUser(id, userData) {
    const { username, role, name, status, location_id } = userData;

    return new Promise((resolve, reject) => {
      const query = `
        UPDATE users
        SET username = ?, role = ?, name = ?, status = ?, location_id = ?
        WHERE id = ?
      `;

      this.db.run(query, [username, role, name, status, location_id, id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  async updateUserPassword(id, newPassword) {
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    return new Promise((resolve, reject) => {
      const query = `UPDATE users SET password = ? WHERE id = ?`;

      this.db.run(query, [hashedPassword, id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  deleteUser(id) {
    return new Promise((resolve, reject) => {
      // First delete user permissions, then delete user
      this.db.run(`DELETE FROM user_permissions WHERE user_id = ?`, [id], (err) => {
        if (err) {
          console.error('Error deleting user permissions:', err);
        }

        // Then delete the user
        const query = `DELETE FROM users WHERE id = ?`;
        this.db.run(query, [id], function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ changes: this.changes });
          }
        });
      });
    });
  }

  // Permission Management Functions
  async createUserPermissions(userId, permissions) {
    return new Promise((resolve, reject) => {
      // First delete existing permissions for this user
      this.db.run(`DELETE FROM user_permissions WHERE user_id = ?`, [userId], (err) => {
        if (err) {
          reject(err);
          return;
        }

        // Insert new permissions
        const insertQuery = `
          INSERT INTO user_permissions (user_id, module_id, module_name, can_view, can_edit, can_delete)
          VALUES (?, ?, ?, ?, ?, ?)
        `;

        let completed = 0;
        let hasError = false;

        if (permissions.length === 0) {
          resolve({ success: true });
          return;
        }

        permissions.forEach(permission => {
          this.db.run(insertQuery, [
            userId,
            permission.module_id,
            permission.module_name,
            permission.can_view ? 1 : 0,
            permission.can_edit ? 1 : 0,
            permission.can_delete ? 1 : 0
          ], (err) => {
            completed++;
            if (err && !hasError) {
              hasError = true;
              reject(err);
            } else if (completed === permissions.length && !hasError) {
              resolve({ success: true });
            }
          });
        });
      });
    });
  }

  getUserPermissions(userId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT module_id, module_name, can_view, can_edit, can_delete
        FROM user_permissions
        WHERE user_id = ?
      `;

      this.db.all(query, [userId], (err, permissions) => {
        if (err) {
          reject(err);
        } else {
          resolve(permissions);
        }
      });
    });
  }

  getUserByUsername(username) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM users WHERE username = ?`;

      this.db.get(query, [username], (err, user) => {
        if (err) {
          reject(err);
        } else {
          resolve(user);
        }
      });
    });
  }

  // Product Management Functions
  async createProduct(productData) {
    const {
      barcode, description, category, subcategory, supplier, brand_name, purchase_price,
      style, color, size, min_qty, max_qty, image_path,
      special_discount, priority, image_confirm, non_scanable, daily_item
    } = productData;

    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO products (
          barcode, description, category, subcategory, supplier, brand_name, purchase_price,
          style, color, size, min_qty, max_qty, image_path,
          special_discount, priority, image_confirm, non_scanable, daily_item,
          updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `;

      this.db.run(query, [
        barcode, description, category, subcategory, supplier, brand_name, purchase_price,
        style, color, size, min_qty || 0, max_qty || 0, image_path,
        special_discount ? 1 : 0, priority ? 1 : 0, image_confirm ? 1 : 0,
        non_scanable ? 1 : 0, daily_item ? 1 : 0
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, barcode, description });
        }
      });
    });
  }

  getAllProducts() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM products
        ORDER BY created_at DESC
      `;

      this.db.all(query, [], (err, products) => {
        if (err) {
          reject(err);
        } else {
          resolve(products);
        }
      });
    });
  }

  // Location-filtered product methods (using purchase_price from products table)
  getProductsByLocation(locationId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT DISTINCT p.*, ls.stock
        FROM products p
        INNER JOIN location_stocks ls ON p.id = ls.product_id
        INNER JOIN locations l ON ls.location = l.location
        WHERE l.id = ?
        ORDER BY p.created_at DESC
      `;

      this.db.all(query, [locationId], (err, products) => {
        if (err) {
          reject(err);
        } else {
          resolve(products);
        }
      });
    });
  }

  getProductsByLocationName(locationName) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT DISTINCT p.*
        FROM products p
        INNER JOIN location_stocks ls ON p.id = ls.product_id
        WHERE ls.location = ?
        ORDER BY p.created_at DESC
      `;

      this.db.all(query, [locationName], (err, products) => {
        if (err) {
          reject(err);
        } else {
          resolve(products);
        }
      });
    });
  }

  // Get only non-scannable products by location for the Select Item modal
  getNonScannableProductsByLocation(locationId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT DISTINCT p.*, ls.stock
        FROM products p
        INNER JOIN location_stocks ls ON p.id = ls.product_id
        INNER JOIN locations l ON ls.location = l.location
        WHERE l.id = ? AND p.non_scanable = 1
        ORDER BY p.brand_name ASC, p.purchase_price ASC
      `;

      this.db.all(query, [locationId], (err, products) => {
        if (err) {
          reject(err);
        } else {
          resolve(products);
        }
      });
    });
  }

  getProductById(id) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM products WHERE id = ?`;

      this.db.get(query, [id], (err, product) => {
        if (err) {
          reject(err);
        } else {
          resolve(product);
        }
      });
    });
  }

  getProductByBarcode(barcode) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM products WHERE barcode = ?`;

      this.db.get(query, [barcode], (err, product) => {
        if (err) {
          reject(err);
        } else {
          resolve(product);
        }
      });
    });
  }

  async updateProduct(id, productData) {
    const {
      barcode, description, category, subcategory, supplier, brand_name, purchase_price,
      style, color, size, min_qty, max_qty, image_path,
      special_discount, priority, image_confirm, non_scanable, daily_item
    } = productData;

    return new Promise((resolve, reject) => {
      const query = `
        UPDATE products SET
          barcode = ?, description = ?, category = ?, subcategory = ?, supplier = ?, brand_name = ?,
          purchase_price = ?, style = ?, color = ?, size = ?, min_qty = ?, max_qty = ?,
          image_path = ?, special_discount = ?, priority = ?, image_confirm = ?,
          non_scanable = ?, daily_item = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      this.db.run(query, [
        barcode, description, category, subcategory, supplier, brand_name, purchase_price,
        style, color, size, min_qty || 0, max_qty || 0, image_path,
        special_discount ? 1 : 0, priority ? 1 : 0, image_confirm ? 1 : 0,
        non_scanable ? 1 : 0, daily_item ? 1 : 0, id
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  deleteProduct(id) {
    return new Promise((resolve, reject) => {
      // First delete location stocks, then delete product
      this.db.run(`DELETE FROM location_stocks WHERE product_id = ?`, [id], (err) => {
        if (err) {
          console.error('Error deleting product location stocks:', err);
        }

        // Then delete the product
        const query = `DELETE FROM products WHERE id = ?`;
        this.db.run(query, [id], function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ changes: this.changes });
          }
        });
      });
    });
  }

  // Location Stock Management Functions
  async createLocationStocks(productId, locationStocks, minQty = null, maxQty = null) {
    return new Promise((resolve, reject) => {
      // Get product's min_qty and max_qty if not provided
      if (minQty === null || maxQty === null) {
        this.db.get(`SELECT min_qty, max_qty FROM products WHERE id = ?`, [productId], (err, product) => {
          if (err) {
            reject(err);
            return;
          }

          if (!product) {
            reject(new Error(`Product with ID ${productId} not found`));
            return;
          }

          // Recursively call with the retrieved values
          this.createLocationStocks(productId, locationStocks, product.min_qty, product.max_qty)
            .then(resolve)
            .catch(reject);
        });
        return;
      }

      // First delete existing location stocks for this product
      this.db.run(`DELETE FROM location_stocks WHERE product_id = ?`, [productId], (err) => {
        if (err) {
          reject(err);
          return;
        }

        // Insert new location stocks with min_qty
        const insertQuery = `
          INSERT INTO location_stocks (product_id, location, stock, min_qty, price, updated_at)
          VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        `;

        let completed = 0;
        let hasError = false;

        if (locationStocks.length === 0) {
          resolve({ success: true });
          return;
        }

        locationStocks.forEach(locationStock => {
          // Use maxQty as initial stock if stock is not provided or is 0
          const initialStock = locationStock.stock || maxQty || 0;

          this.db.run(insertQuery, [
            productId,
            locationStock.location,
            initialStock,
            minQty || 0,
            locationStock.price || 0.00
          ], (err) => {
            completed++;
            if (err && !hasError) {
              hasError = true;
              reject(err);
            } else if (completed === locationStocks.length && !hasError) {
              resolve({ success: true });
            }
          });
        });
      });
    });
  }

  getLocationStocksByProductId(productId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT location, stock, min_qty, price
        FROM location_stocks
        WHERE product_id = ?
        ORDER BY location
      `;

      this.db.all(query, [productId], (err, stocks) => {
        if (err) {
          reject(err);
        } else {
          resolve(stocks);
        }
      });
    });
  }

  async updateLocationStocks(productId, locationStocks, minQty = null) {
    return new Promise((resolve, reject) => {
      // Get product's min_qty if not provided
      if (minQty === null) {
        this.db.get(`SELECT min_qty FROM products WHERE id = ?`, [productId], (err, product) => {
          if (err) {
            reject(err);
            return;
          }

          if (!product) {
            reject(new Error(`Product with ID ${productId} not found`));
            return;
          }

          // Recursively call with the retrieved min_qty
          this.updateLocationStocks(productId, locationStocks, product.min_qty)
            .then(resolve)
            .catch(reject);
        });
        return;
      }

      // Delete existing stocks and insert new ones
      this.db.run(`DELETE FROM location_stocks WHERE product_id = ?`, [productId], (err) => {
        if (err) {
          reject(err);
          return;
        }

        // Insert updated location stocks with min_qty
        const insertQuery = `
          INSERT INTO location_stocks (product_id, location, stock, min_qty, price, updated_at)
          VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        `;

        let completed = 0;
        let hasError = false;

        if (locationStocks.length === 0) {
          resolve({ success: true });
          return;
        }

        locationStocks.forEach(locationStock => {
          this.db.run(insertQuery, [
            productId,
            locationStock.location,
            locationStock.stock || 0,
            minQty || 0,
            locationStock.price || 0.00
          ], (err) => {
            completed++;
            if (err && !hasError) {
              hasError = true;
              reject(err);
            } else if (completed === locationStocks.length && !hasError) {
              resolve({ success: true });
            }
          });
        });
      });
    });
  }

  // Category Management Functions
  async createCategory(categoryData) {
    const { code, name, parent_id, description, display_order, status } = categoryData;

    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO categories (code, name, parent_id, description, display_order, status, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `;

      this.db.run(query, [
        code, name, parent_id || null, description, display_order || 0, status || 'active'
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, code, name });
        }
      });
    });
  }

  getAllCategories() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT c.*, p.name as parent_name
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        ORDER BY c.display_order, c.name
      `;

      this.db.all(query, [], (err, categories) => {
        if (err) {
          reject(err);
        } else {
          resolve(categories);
        }
      });
    });
  }

  getCategoryById(id) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT c.*, p.name as parent_name
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        WHERE c.id = ?
      `;

      this.db.get(query, [id], (err, category) => {
        if (err) {
          reject(err);
        } else {
          resolve(category);
        }
      });
    });
  }

  getCategoryByCode(code) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT c.*, p.name as parent_name
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        WHERE c.code = ?
      `;

      this.db.get(query, [code], (err, category) => {
        if (err) {
          reject(err);
        } else {
          resolve(category);
        }
      });
    });
  }

  async updateCategory(id, categoryData) {
    const { code, name, parent_id, description, display_order, status } = categoryData;

    return new Promise((resolve, reject) => {
      const query = `
        UPDATE categories SET
          code = ?, name = ?, parent_id = ?, description = ?, display_order = ?,
          status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      this.db.run(query, [
        code, name, parent_id || null, description, display_order || 0, status || 'active', id
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  deleteCategory(id) {
    return new Promise((resolve, reject) => {
      // First check if category has children
      this.db.get(`SELECT COUNT(*) as count FROM categories WHERE parent_id = ?`, [id], (err, result) => {
        if (err) {
          reject(err);
          return;
        }

        if (result.count > 0) {
          reject(new Error('Cannot delete category with subcategories. Please delete subcategories first.'));
          return;
        }

        // Delete the category
        const query = `DELETE FROM categories WHERE id = ?`;
        this.db.run(query, [id], function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ changes: this.changes });
          }
        });
      });
    });
  }

  getParentCategories() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM categories
        WHERE parent_id IS NULL AND status = 'active'
        ORDER BY display_order, name
      `;

      this.db.all(query, [], (err, categories) => {
        if (err) {
          reject(err);
        } else {
          resolve(categories);
        }
      });
    });
  }

  // Supplier Management Functions
  async createSupplier(supplierData) {
    const {
      name, address1, address2, city, state, zip_code, telephone, fax, email,
      sales_rep, sales_rep_phone, retail_website, wholesale_website, status
    } = supplierData;

    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO suppliers (
          name, address1, address2, city, state, zip_code, telephone, fax, email,
          sales_rep, sales_rep_phone, retail_website, wholesale_website, status, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `;

      this.db.run(query, [
        name, address1, address2, city, state, zip_code, telephone, fax, email,
        sales_rep, sales_rep_phone, retail_website, wholesale_website, status || 'active'
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, name });
        }
      });
    });
  }

  getAllSuppliers() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM suppliers
        ORDER BY name
      `;

      this.db.all(query, [], (err, suppliers) => {
        if (err) {
          reject(err);
        } else {
          resolve(suppliers);
        }
      });
    });
  }

  getSupplierById(id) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM suppliers WHERE id = ?`;

      this.db.get(query, [id], (err, supplier) => {
        if (err) {
          reject(err);
        } else {
          resolve(supplier);
        }
      });
    });
  }

  getSupplierByName(name) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM suppliers WHERE name = ?`;

      this.db.get(query, [name], (err, supplier) => {
        if (err) {
          reject(err);
        } else {
          resolve(supplier);
        }
      });
    });
  }

  async updateSupplier(id, supplierData) {
    const {
      name, address1, address2, city, state, zip_code, telephone, fax, email,
      sales_rep, sales_rep_phone, retail_website, wholesale_website, status
    } = supplierData;

    return new Promise((resolve, reject) => {
      const query = `
        UPDATE suppliers SET
          name = ?, address1 = ?, address2 = ?, city = ?, state = ?, zip_code = ?,
          telephone = ?, fax = ?, email = ?, sales_rep = ?, sales_rep_phone = ?,
          retail_website = ?, wholesale_website = ?, status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      this.db.run(query, [
        name, address1, address2, city, state, zip_code, telephone, fax, email,
        sales_rep, sales_rep_phone, retail_website, wholesale_website, status || 'active', id
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  deleteSupplier(id) {
    return new Promise((resolve, reject) => {
      const query = `DELETE FROM suppliers WHERE id = ?`;
      this.db.run(query, [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  // Location Management Functions
  async createLocation(locationData) {
    const {
      location_code, location, company_name, address1, address2, phone, tax_percent,
      email, app_mode, theater_plu, theater_time, theater_ticket_price, deli, status
    } = locationData;

    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO locations (
          location_code, location, company_name, address1, address2, phone, tax_percent,
          email, app_mode, theater_plu, theater_time, theater_ticket_price, deli, status, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `;

      this.db.run(query, [
        location_code, location, company_name, address1, address2, phone, tax_percent || 0,
        email, app_mode, theater_plu, theater_time, theater_ticket_price || 56.00, deli ? 1 : 0, status || 'active'
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, location_code, location });
        }
      });
    });
  }

  getAllLocations() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM locations
        ORDER BY location_code
      `;

      this.db.all(query, [], (err, locations) => {
        if (err) {
          reject(err);
        } else {
          resolve(locations);
        }
      });
    });
  }

  getLocationById(id) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM locations WHERE id = ?`;

      this.db.get(query, [id], (err, location) => {
        if (err) {
          reject(err);
        } else {
          resolve(location);
        }
      });
    });
  }

  getLocationByCode(location_code) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM locations WHERE location_code = ?`;

      this.db.get(query, [location_code], (err, location) => {
        if (err) {
          reject(err);
        } else {
          resolve(location);
        }
      });
    });
  }

  async updateLocation(id, locationData) {
    const {
      location_code, location, company_name, address1, address2, phone, tax_percent,
      email, app_mode, theater_plu, theater_time, theater_ticket_price, deli, status
    } = locationData;

    return new Promise((resolve, reject) => {
      const query = `
        UPDATE locations SET
          location_code = ?, location = ?, company_name = ?, address1 = ?, address2 = ?,
          phone = ?, tax_percent = ?, email = ?, app_mode = ?, theater_plu = ?,
          theater_time = ?, theater_ticket_price = ?, deli = ?, status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      this.db.run(query, [
        location_code, location, company_name, address1, address2, phone, tax_percent || 0,
        email, app_mode, theater_plu, theater_time, theater_ticket_price || 56.00, deli ? 1 : 0, status || 'active', id
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  deleteLocation(id) {
    return new Promise((resolve, reject) => {
      const query = `DELETE FROM locations WHERE id = ?`;
      this.db.run(query, [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  // Force create permissions table if it doesn't exist
  forceCreatePermissionsTable() {
    const createPermissionsTable = `
      CREATE TABLE IF NOT EXISTS user_permissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        module_id TEXT NOT NULL,
        module_name TEXT NOT NULL,
        can_view INTEGER DEFAULT 0,
        can_edit INTEGER DEFAULT 0,
        can_delete INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        UNIQUE(user_id, module_id)
      )
    `;

    return new Promise((resolve, reject) => {
      this.db.run(createPermissionsTable, (err) => {
        if (err) {
          console.error('Error force creating permissions table:', err);
          reject(err);
        } else {
          console.log('Permissions table force created successfully');
          resolve(true);
        }
      });
    });
  }

  // Add sale_type column to sales table if it doesn't exist
  addSaleTypeColumn() {
    return new Promise((resolve, reject) => {
      // Check if column exists
      this.db.all("PRAGMA table_info(sales)", (err, columns) => {
        if (err) {
          reject(err);
          return;
        }

        const hasColumn = columns.some(col => col.name === 'sale_type');
        if (!hasColumn) {
          this.db.run("ALTER TABLE sales ADD COLUMN sale_type TEXT DEFAULT 'sale'", (err) => {
            if (err) {
              console.error('Error adding sale_type column:', err);
              reject(err);
            } else {
              console.log('sale_type column added successfully');
              resolve();
            }
          });
        } else {
          console.log('sale_type column already exists');
          resolve();
        }
      });
    });
  }

  // Add sales type columns to shifts table if they don't exist
  addShiftSalesTypeColumns() {
    return new Promise((resolve, reject) => {
      // Check if columns exist
      this.db.all("PRAGMA table_info(shifts)", (err, columns) => {
        if (err) {
          reject(err);
          return;
        }

        const columnNames = columns.map(col => col.name);
        const columnsToAdd = [
          { name: 'total_sale_sales', type: 'DECIMAL(10,2) DEFAULT 0' },
          { name: 'total_sale_transactions', type: 'INTEGER DEFAULT 0' },
          { name: 'total_theater_sales', type: 'DECIMAL(10,2) DEFAULT 0' },
          { name: 'total_theater_transactions', type: 'INTEGER DEFAULT 0' },
          { name: 'total_deli_sales', type: 'DECIMAL(10,2) DEFAULT 0' },
          { name: 'total_deli_transactions', type: 'INTEGER DEFAULT 0' }
        ];

        const missingColumns = columnsToAdd.filter(col => !columnNames.includes(col.name));

        if (missingColumns.length === 0) {
          console.log('All shift sales type columns already exist');
          resolve();
          return;
        }

        let promises = missingColumns.map(col => {
          return new Promise((res, rej) => {
            this.db.run(`ALTER TABLE shifts ADD COLUMN ${col.name} ${col.type}`, (err) => {
              if (err) {
                console.error(`Error adding ${col.name} column:`, err);
                rej(err);
              } else {
                console.log(`${col.name} column added successfully`);
                res();
              }
            });
          });
        });

        Promise.all(promises).then(() => {
          console.log('All missing shift sales type columns added successfully');
          resolve();
        }).catch(reject);
      });
    });
  }

  // Add new shift management columns if they don't exist
  addShiftManagementColumns() {
    return new Promise((resolve, reject) => {
      // Check if columns exist
      this.db.all("PRAGMA table_info(shifts)", (err, columns) => {
        if (err) {
          reject(err);
          return;
        }

        const columnNames = columns.map(col => col.name);
        const columnsToAdd = [
          { name: 'shift_type', type: 'TEXT CHECK (shift_type IN (\'day\', \'night\'))' },
          { name: 'scheduled_start_time', type: 'DATETIME' },
          { name: 'scheduled_end_time', type: 'DATETIME' },
          { name: 'overtime_minutes', type: 'INTEGER DEFAULT 0' },
          { name: 'carryover_minutes', type: 'INTEGER DEFAULT 0' },
          { name: 'previous_shift_id', type: 'TEXT' },
          { name: 'login_time', type: 'DATETIME' }
        ];

        const missingColumns = columnsToAdd.filter(col => !columnNames.includes(col.name));

        if (missingColumns.length === 0) {
          console.log('All shift management columns already exist');
          resolve();
          return;
        }

        let promises = missingColumns.map(col => {
          return new Promise((res, rej) => {
            this.db.run(`ALTER TABLE shifts ADD COLUMN ${col.name} ${col.type}`, (err) => {
              if (err) {
                console.error(`Error adding ${col.name} column:`, err);
                rej(err);
              } else {
                console.log(`${col.name} column added successfully`);
                res();
              }
            });
          });
        });

        Promise.all(promises).then(() => {
          console.log('All missing shift management columns added successfully');
          resolve();
        }).catch(reject);
      });
    });
  }

  // Add price columns to tickets table if they don't exist
  addTicketPriceColumns() {
    return new Promise((resolve, reject) => {
      // Check if columns exist
      this.db.all("PRAGMA table_info(tickets)", (err, columns) => {
        if (err) {
          reject(err);
          return;
        }

        const hasTicketPrice = columns.some(col => col.name === 'ticket_price');
        const hasTotalAmount = columns.some(col => col.name === 'total_amount');

        let promises = [];

        if (!hasTicketPrice) {
          promises.push(new Promise((res, rej) => {
            this.db.run("ALTER TABLE tickets ADD COLUMN ticket_price DECIMAL(10,2) DEFAULT 56.00", (err) => {
              if (err) {
                console.error('Error adding ticket_price column:', err);
                rej(err);
              } else {
                console.log('ticket_price column added successfully');
                res();
              }
            });
          }));
        }

        if (!hasTotalAmount) {
          promises.push(new Promise((res, rej) => {
            this.db.run("ALTER TABLE tickets ADD COLUMN total_amount DECIMAL(10,2) DEFAULT 56.00", (err) => {
              if (err) {
                console.error('Error adding total_amount column to tickets:', err);
                rej(err);
              } else {
                console.log('total_amount column added to tickets successfully');
                res();
              }
            });
          }));
        }

        if (promises.length === 0) {
          console.log('Ticket price columns already exist');
          resolve();
        } else {
          Promise.all(promises).then(() => {
            // Update existing tickets with default price
            this.db.run(`
              UPDATE tickets
              SET ticket_price = 56.00, total_amount = 56.00
              WHERE ticket_price IS NULL OR total_amount IS NULL
            `, (err) => {
              if (err) {
                console.error('Error updating existing tickets with default price:', err);
                reject(err);
              } else {
                console.log('Existing tickets updated with default price');
                resolve();
              }
            });
          }).catch(reject);
        }
      });
    });
  }

  // Ticket management methods
  createTicket(ticketData) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        console.error('Database connection not available');
        reject(new Error('Database connection not available'));
        return;
      }

      // Calculate ticket price (default $56.00 per ticket regardless of duration)
      const ticketPrice = ticketData.ticket_price || 56.00;
      const totalAmount = ticketPrice; // For now, total equals ticket price

      const query = `
        INSERT INTO tickets (
          ticket_id, duration, photo_path, payment_method,
          ticket_price, total_amount,
          user_id, location_id, operator_name, location_name,
          issued_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime(CURRENT_TIMESTAMP, 'localtime'))
      `;

      const values = [
        ticketData.ticket_id,
        ticketData.duration,
        ticketData.photo_path,
        ticketData.payment_method,
        ticketPrice,
        totalAmount,
        ticketData.user_id,
        ticketData.location_id,
        ticketData.operator_name,
        ticketData.location_name
      ];

      this.db.run(query, values, function(err) {
        if (err) {
          console.error('Error creating ticket:', err);
          reject(err);
        } else {
          console.log('Ticket created successfully with ID:', this.lastID);
          resolve({
            id: this.lastID,
            ticket_id: ticketData.ticket_id,
            success: true
          });
        }
      });
    });
  }

  getAllTickets() {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        console.error('Database connection not available for getAllTickets');
        reject(new Error('Database connection not available'));
        return;
      }

      const query = `
        SELECT t.*, u.username, l.location_code
        FROM tickets t
        LEFT JOIN users u ON t.user_id = u.id
        LEFT JOIN locations l ON t.location_id = l.id
        ORDER BY t.issued_at DESC
      `;

      this.db.all(query, [], (err, rows) => {
        if (err) {
          console.error('Error fetching tickets:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // Location-filtered ticket methods
  getTicketsByLocation(locationId) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        console.error('Database connection not available for getTicketsByLocation');
        reject(new Error('Database connection not available'));
        return;
      }

      const query = `
        SELECT t.*, u.username, l.location_code
        FROM tickets t
        LEFT JOIN users u ON t.user_id = u.id
        LEFT JOIN locations l ON t.location_id = l.id
        WHERE t.location_id = ?
        ORDER BY t.issued_at DESC
      `;

      this.db.all(query, [locationId], (err, rows) => {
        if (err) {
          console.error('Error fetching tickets by location:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  getTicketById(ticketId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT t.*, u.username, l.location_code
        FROM tickets t
        LEFT JOIN users u ON t.user_id = u.id
        LEFT JOIN locations l ON t.location_id = l.id
        WHERE t.ticket_id = ?
      `;

      this.db.get(query, [ticketId], (err, row) => {
        if (err) {
          console.error('Error fetching ticket:', err);
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // Banned ticket methods
  banTicket(ticketId, banData) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database connection not available'));
        return;
      }

      const db = this.db; // Store reference to avoid context issues

      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        // First get the original ticket data
        db.get('SELECT * FROM tickets WHERE id = ?', [ticketId], (err, ticket) => {
          if (err) {
            db.run('ROLLBACK');
            reject(err);
            return;
          }

          if (!ticket) {
            db.run('ROLLBACK');
            reject(new Error('Ticket not found'));
            return;
          }

          // Insert into banned_tickets table
          const query = `
            INSERT INTO banned_tickets (
              original_ticket_id, ticket_id, duration, photo_path, payment_method,
              user_id, location_id, operator_name, location_name, issued_at,
              banned_by_user_id, banned_by_operator, ban_reason, action_type
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `;

          const values = [
            ticket.id, ticket.ticket_id, ticket.duration, ticket.photo_path,
            ticket.payment_method, ticket.user_id, ticket.location_id,
            ticket.operator_name, ticket.location_name, ticket.issued_at,
            banData.banned_by_user_id, banData.banned_by_operator,
            banData.ban_reason, banData.action_type
          ];

          db.run(query, values, function(err) {
            if (err) {
              db.run('ROLLBACK');
              reject(err);
              return;
            }

            const bannedTicketId = this.lastID;

            // Update original ticket status to 'banned'
            db.run('UPDATE tickets SET status = ? WHERE id = ?', ['banned', ticketId], (err) => {
              if (err) {
                db.run('ROLLBACK');
                reject(err);
                return;
              }

              db.run('COMMIT', (err) => {
                if (err) {
                  reject(err);
                } else {
                  resolve({ id: bannedTicketId, original_ticket_id: ticketId });
                }
              });
            });
          });
        });
      });
    });
  }

  getBannedTickets() {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database connection not available'));
        return;
      }

      const query = `
        SELECT bt.*,
               u.username as banned_by_username,
               ru.username as refunded_by_username
        FROM banned_tickets bt
        LEFT JOIN users u ON bt.banned_by_user_id = u.id
        LEFT JOIN users ru ON bt.refunded_by_user_id = ru.id
        ORDER BY bt.banned_at DESC
      `;

      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // Location-filtered banned tickets method
  getBannedTicketsByLocation(locationId) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database connection not available'));
        return;
      }

      const query = `
        SELECT bt.*,
               u.username as banned_by_username,
               ru.username as refunded_by_username
        FROM banned_tickets bt
        LEFT JOIN users u ON bt.banned_by_user_id = u.id
        LEFT JOIN users ru ON bt.refunded_by_user_id = ru.id
        WHERE bt.location_id = ?
        ORDER BY bt.banned_at DESC
      `;

      this.db.all(query, [locationId], (err, rows) => {
        if (err) {
          console.error('Error fetching banned tickets by location:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  refundBannedTicket(bannedTicketId, refundData) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database connection not available'));
        return;
      }

      const db = this.db; // Store reference to avoid context issues

      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        // First get the banned ticket to find the original ticket ID
        db.get('SELECT original_ticket_id FROM banned_tickets WHERE id = ?', [bannedTicketId], (err, bannedTicket) => {
          if (err) {
            db.run('ROLLBACK');
            reject(err);
            return;
          }

          if (!bannedTicket) {
            db.run('ROLLBACK');
            reject(new Error('Banned ticket not found'));
            return;
          }

          // Update the banned_tickets table with refund information
          const updateBannedQuery = `
            UPDATE banned_tickets
            SET is_refunded = 1,
                refund_amount = ?,
                refunded_at = datetime(CURRENT_TIMESTAMP, 'localtime'),
                refunded_by_user_id = ?,
                refunded_by_operator = ?
            WHERE id = ?
          `;

          const bannedValues = [
            refundData.refund_amount,
            refundData.refunded_by_user_id,
            refundData.refunded_by_operator,
            bannedTicketId
          ];

          db.run(updateBannedQuery, bannedValues, function(err) {
            if (err) {
              db.run('ROLLBACK');
              reject(err);
              return;
            }

            // Update the original tickets table status to 'refunded'
            db.run('UPDATE tickets SET status = ? WHERE id = ?', ['refunded', bannedTicket.original_ticket_id], (err) => {
              if (err) {
                db.run('ROLLBACK');
                reject(err);
                return;
              }

              db.run('COMMIT', (err) => {
                if (err) {
                  reject(err);
                } else {
                  resolve({
                    id: bannedTicketId,
                    original_ticket_id: bannedTicket.original_ticket_id,
                    changes: this.changes
                  });
                }
              });
            });
          });
        });
      });
    });
  }

  // Draft Sales Management Functions
  async createDraftSale(draftData) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database connection not available'));
        return;
      }

      const db = this.db;
      const {
        draftSaleId, userId, locationId, operatorName, locationName,
        itemCount, totalAmount, items
      } = draftData;

      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        // Insert main draft sale record
        const draftQuery = `
          INSERT INTO draft_sales (
            draft_sale_id, user_id, location_id, operator_name, location_name,
            item_count, total_amount, status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, 'draft')
        `;

        db.run(draftQuery, [
          draftSaleId, userId, locationId, operatorName, locationName,
          itemCount, totalAmount
        ], function(err) {
          if (err) {
            db.run('ROLLBACK');
            reject(err);
            return;
          }

          // Insert draft sale items
          const itemQuery = `
            INSERT INTO draft_sales_items (
              draft_sale_id, product_id, product_name, product_barcode,
              product_category, product_subcategory, quantity, unit_price, total_price, discount
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `;

          let itemsProcessed = 0;
          let hasError = false;

          if (items.length === 0) {
            db.run('COMMIT');
            resolve({ draftSaleId, id: this.lastID });
            return;
          }

          items.forEach(item => {
            db.run(itemQuery, [
              draftSaleId, item.productId, item.name, item.barcode,
              item.category, item.subcategory, item.quantity, item.price, item.total, item.discount || 0
            ], function(err) {
              if (err && !hasError) {
                hasError = true;
                db.run('ROLLBACK');
                reject(err);
                return;
              }

              itemsProcessed++;
              if (itemsProcessed === items.length && !hasError) {
                db.run('COMMIT');
                resolve({ draftSaleId, id: this.lastID });
              }
            });
          });
        });
      });
    });
  }

  // Get draft sales by location (for location-based access control)
  getDraftSalesByLocation(locationId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT ds.*, COUNT(dsi.id) as item_count_actual
        FROM draft_sales ds
        LEFT JOIN draft_sales_items dsi ON ds.draft_sale_id = dsi.draft_sale_id
        WHERE ds.location_id = ? AND ds.status = 'draft'
        GROUP BY ds.id
        ORDER BY ds.created_at DESC
      `;

      this.db.all(query, [locationId], (err, drafts) => {
        if (err) {
          reject(err);
        } else {
          resolve(drafts);
        }
      });
    });
  }

  // Get all draft sales (admin only)
  getAllDraftSales() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT ds.*, COUNT(dsi.id) as item_count_actual
        FROM draft_sales ds
        LEFT JOIN draft_sales_items dsi ON ds.draft_sale_id = dsi.draft_sale_id
        WHERE ds.status = 'draft'
        GROUP BY ds.id
        ORDER BY ds.created_at DESC
      `;

      this.db.all(query, [], (err, drafts) => {
        if (err) {
          reject(err);
        } else {
          resolve(drafts);
        }
      });
    });
  }

  // Get draft sale details with items
  getDraftSaleDetails(draftSaleId) {
    return new Promise((resolve, reject) => {
      const draftQuery = `SELECT * FROM draft_sales WHERE draft_sale_id = ? AND status = 'draft'`;
      const itemsQuery = `SELECT * FROM draft_sales_items WHERE draft_sale_id = ?`;

      this.db.get(draftQuery, [draftSaleId], (err, draft) => {
        if (err) {
          reject(err);
          return;
        }

        if (!draft) {
          resolve(null);
          return;
        }

        this.db.all(itemsQuery, [draftSaleId], (err, items) => {
          if (err) {
            reject(err);
          } else {
            resolve({ ...draft, items });
          }
        });
      });
    });
  }

  // Delete draft sale
  deleteDraftSale(draftSaleId) {
    return new Promise((resolve, reject) => {
      const query = `DELETE FROM draft_sales WHERE draft_sale_id = ?`;

      this.db.run(query, [draftSaleId], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  // Sales Management Functions
  async createSale(saleData) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database connection not available'));
        return;
      }

      const db = this.db;
      const self = this; // Store reference to Database instance
      const {
        saleId, userId, locationId, operatorName, locationName,
        subtotal, taxAmount, discountAmount, totalAmount,
        paymentCash, paymentDebit, paymentCredit, paymentTotal,
        changeAmount, itemCount, items
      } = saleData;

      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        // Determine sale type based on items
        let saleType = 'sale';
        if (items && items.length > 0) {
          // Check if any item is a daily item (deli)
          const hasDeliItems = items.some(item => item.isDeliItem);
          console.log('🔍 DELI DEBUG - Sale creation analysis:');
          console.log(`  Total items: ${items.length}`);
          console.log(`  Items with isDeliItem flag:`, items.filter(item => item.isDeliItem));
          console.log(`  Has deli items: ${hasDeliItems}`);

          if (hasDeliItems) {
            saleType = 'deli';
            console.log(`✅ DELI: Sale marked as 'deli' type`);
          } else {
            console.log(`📦 REGULAR: Sale marked as 'sale' type`);
          }
        }

        // Insert main sale record
        const saleQuery = `
          INSERT INTO sales (
            sale_id, user_id, location_id, operator_name, location_name,
            subtotal, tax_amount, discount_amount, total_amount,
            payment_cash, payment_debit, payment_credit, payment_total,
            change_amount, item_count, sale_type, status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'completed')
        `;

        db.run(saleQuery, [
          saleId, userId, locationId, operatorName, locationName,
          subtotal, taxAmount, discountAmount, totalAmount,
          paymentCash, paymentDebit, paymentCredit, paymentTotal,
          changeAmount, itemCount, saleType
        ], function(err) {
          if (err) {
            db.run('ROLLBACK');
            reject(err);
            return;
          }

          // Insert sale items
          const itemQuery = `
            INSERT INTO sales_items (
              sale_id, product_id, product_name, product_barcode,
              product_category, product_subcategory, quantity, unit_price, total_price
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          `;

          let itemsProcessed = 0;
          let hasError = false;

          if (items.length === 0) {
            db.run('COMMIT');

            // Update shift totals after successful sale
            self.updateShiftTotalsForSale(userId, locationId, totalAmount, saleType)
              .then(() => {
                console.log(`✅ Sale ${saleId} completed and shift totals updated`);
                resolve({ saleId, id: this.lastID });
              })
              .catch(shiftErr => {
                console.error('Error updating shift totals:', shiftErr);
                // Don't fail the sale if shift update fails
                resolve({ saleId, id: this.lastID });
              });
            return;
          }

          items.forEach(item => {
            db.run(itemQuery, [
              saleId, item.productId, item.name, item.barcode,
              item.category, item.subcategory, item.quantity, item.price, item.total
            ], function(err) {
              if (err && !hasError) {
                hasError = true;
                db.run('ROLLBACK');
                reject(err);
                return;
              }

              itemsProcessed++;
              if (itemsProcessed === items.length && !hasError) {
                db.run('COMMIT');

                // Update shift totals after successful sale
                self.updateShiftTotalsForSale(userId, locationId, totalAmount, saleType)
                  .then(() => {
                    console.log(`✅ Sale ${saleId} completed and shift totals updated`);
                    resolve({ saleId, id: this.lastID });
                  })
                  .catch(shiftErr => {
                    console.error('Error updating shift totals:', shiftErr);
                    // Don't fail the sale if shift update fails
                    resolve({ saleId, id: this.lastID });
                  });
              }
            });
          });
        });
      });
    });
  }

  // Get sales by location (for location-based access control)
  getSalesByLocation(locationId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT s.*, COUNT(si.id) as item_count_actual
        FROM sales s
        LEFT JOIN sales_items si ON s.sale_id = si.sale_id
        WHERE s.location_id = ?
        GROUP BY s.id
        ORDER BY s.sale_date DESC
      `;

      this.db.all(query, [locationId], (err, sales) => {
        if (err) {
          reject(err);
        } else {
          resolve(sales);
        }
      });
    });
  }

  // Daily Sales Report Methods
  getDailySalesReport(locationId, dateFrom, dateTo, shift = 'both', operator = 'all') {
    return new Promise((resolve, reject) => {
      let timeCondition = '';

      // Add shift filtering based on time
      if (shift === 'day') {
        timeCondition = " AND strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18'";
      } else if (shift === 'night') {
        timeCondition = " AND (strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06')";
      }

      // Add operator filtering
      let operatorCondition = '';
      let queryParams = [locationId, dateFrom, dateTo];
      if (operator && operator !== 'all') {
        operatorCondition = " AND s.operator_name = ?";
        queryParams.push(operator);
      }

      // Determine GROUP BY clause based on operator filter
      const groupByClause = operator === 'all'
        ? "GROUP BY DATE(s.sale_date), s.location_name"
        : "GROUP BY DATE(s.sale_date), s.location_name, s.operator_name";

      // Set operator_name field based on filter
      const operatorNameField = operator === 'all'
        ? "'All Operators' as operator_name"
        : "s.operator_name";

      const query = `
        SELECT
          DATE(s.sale_date) as invoice_date,
          'sale' as sale_type,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18' THEN s.payment_cash ELSE 0 END) as day_cash,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18' THEN s.payment_debit ELSE 0 END) as day_debit,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18' THEN s.payment_credit ELSE 0 END) as day_credit,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18' THEN s.total_amount ELSE 0 END) as day_total,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06' THEN s.payment_cash ELSE 0 END) as night_cash,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06' THEN s.payment_debit ELSE 0 END) as night_debit,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06' THEN s.payment_credit ELSE 0 END) as night_credit,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06' THEN s.total_amount ELSE 0 END) as night_total,
          SUM(s.payment_cash) as total_cash,
          SUM(s.payment_debit) as total_debit,
          SUM(s.payment_credit) as total_credit,
          SUM(s.total_amount) as total_amount,
          s.location_name,
          ${operatorNameField}
        FROM sales s
        WHERE s.location_id = ?
          AND DATE(s.sale_date) >= ?
          AND DATE(s.sale_date) <= ?
          AND (s.sale_type = 'sale' OR s.sale_type IS NULL)
          ${timeCondition}
          ${operatorCondition}
        ${groupByClause}
        ORDER BY DATE(s.sale_date) DESC
      `;

      this.db.all(query, queryParams, (err, sales) => {
        if (err) {
          reject(err);
        } else {
          resolve(sales);
        }
      });
    });
  }

  getTheaterSalesReport(locationId, dateFrom, dateTo, shift = 'both', operator = 'all') {
    return new Promise((resolve, reject) => {
      let timeCondition = '';

      if (shift === 'day') {
        timeCondition = " AND strftime('%H', t.issued_at) >= '06' AND strftime('%H', t.issued_at) < '18'";
      } else if (shift === 'night') {
        timeCondition = " AND (strftime('%H', t.issued_at) >= '18' OR strftime('%H', t.issued_at) < '06')";
      }

      // Add operator filtering
      let operatorCondition = '';
      let queryParams = [locationId, dateFrom, dateTo];
      if (operator && operator !== 'all') {
        operatorCondition = " AND t.operator_name = ?";
        queryParams.push(operator);
      }

      // Determine GROUP BY clause based on operator filter
      const groupByClause = operator === 'all'
        ? "GROUP BY DATE(t.issued_at), t.location_name"
        : "GROUP BY DATE(t.issued_at), t.location_name, t.operator_name";

      // Set operator_name field based on filter
      const operatorNameField = operator === 'all'
        ? "'All Operators' as operator_name"
        : "t.operator_name";

      const query = `
        SELECT
          DATE(t.issued_at) as invoice_date,
          'theater' as sale_type,
          SUM(CASE WHEN strftime('%H', t.issued_at) >= '06' AND strftime('%H', t.issued_at) < '18'
                   AND t.payment_method = 'cash' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as day_cash,
          SUM(CASE WHEN strftime('%H', t.issued_at) >= '06' AND strftime('%H', t.issued_at) < '18'
                   AND t.payment_method = 'debit' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as day_debit,
          SUM(CASE WHEN strftime('%H', t.issued_at) >= '06' AND strftime('%H', t.issued_at) < '18'
                   AND t.payment_method = 'credit' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as day_credit,
          SUM(CASE WHEN strftime('%H', t.issued_at) >= '06' AND strftime('%H', t.issued_at) < '18'
                   THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as day_total,
          SUM(CASE WHEN (strftime('%H', t.issued_at) >= '18' OR strftime('%H', t.issued_at) < '06')
                   AND t.payment_method = 'cash' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as night_cash,
          SUM(CASE WHEN (strftime('%H', t.issued_at) >= '18' OR strftime('%H', t.issued_at) < '06')
                   AND t.payment_method = 'debit' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as night_debit,
          SUM(CASE WHEN (strftime('%H', t.issued_at) >= '18' OR strftime('%H', t.issued_at) < '06')
                   AND t.payment_method = 'credit' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as night_credit,
          SUM(CASE WHEN (strftime('%H', t.issued_at) >= '18' OR strftime('%H', t.issued_at) < '06')
                   THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as night_total,
          SUM(CASE WHEN t.payment_method = 'cash' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as total_cash,
          SUM(CASE WHEN t.payment_method = 'debit' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as total_debit,
          SUM(CASE WHEN t.payment_method = 'credit' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as total_credit,
          SUM(COALESCE(t.total_amount, 56.00)) as total_amount,
          t.location_name,
          ${operatorNameField}
        FROM tickets t
        WHERE t.location_id = ?
          AND DATE(t.issued_at) >= ?
          AND DATE(t.issued_at) <= ?
          AND t.status = 'active'
          ${timeCondition}
          ${operatorCondition}
        ${groupByClause}
        ORDER BY DATE(t.issued_at) DESC
      `;

      this.db.all(query, queryParams, (err, tickets) => {
        if (err) {
          reject(err);
        } else {
          resolve(tickets);
        }
      });
    });
  }

  getDeliSalesReport(locationId, dateFrom, dateTo, shift = 'both', operator = 'all') {
    return new Promise((resolve, reject) => {
      let timeCondition = '';

      if (shift === 'day') {
        timeCondition = " AND strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18'";
      } else if (shift === 'night') {
        timeCondition = " AND (strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06')";
      }

      // Add operator filtering
      let operatorCondition = '';
      let queryParams = [locationId, dateFrom, dateTo];
      if (operator && operator !== 'all') {
        operatorCondition = " AND s.operator_name = ?";
        queryParams.push(operator);
      }

      // Determine GROUP BY clause based on operator filter
      const groupByClause = operator === 'all'
        ? "GROUP BY DATE(s.sale_date), s.location_name"
        : "GROUP BY DATE(s.sale_date), s.location_name, s.operator_name";

      // Set operator_name field based on filter
      const operatorNameField = operator === 'all'
        ? "'All Operators' as operator_name"
        : "s.operator_name";

      const query = `
        SELECT
          DATE(s.sale_date) as invoice_date,
          'deli' as sale_type,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18' THEN s.payment_cash ELSE 0 END) as day_cash,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18' THEN s.payment_debit ELSE 0 END) as day_debit,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18' THEN s.payment_credit ELSE 0 END) as day_credit,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18' THEN s.total_amount ELSE 0 END) as day_total,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06' THEN s.payment_cash ELSE 0 END) as night_cash,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06' THEN s.payment_debit ELSE 0 END) as night_debit,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06' THEN s.payment_credit ELSE 0 END) as night_credit,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06' THEN s.total_amount ELSE 0 END) as night_total,
          SUM(s.payment_cash) as total_cash,
          SUM(s.payment_debit) as total_debit,
          SUM(s.payment_credit) as total_credit,
          SUM(s.total_amount) as total_amount,
          s.location_name,
          ${operatorNameField}
        FROM sales s
        WHERE s.location_id = ?
          AND DATE(s.sale_date) >= ?
          AND DATE(s.sale_date) <= ?
          AND s.sale_type = 'deli'
          ${timeCondition}
          ${operatorCondition}
        ${groupByClause}
        ORDER BY DATE(s.sale_date) DESC
      `;

      this.db.all(query, queryParams, (err, deliSales) => {
        if (err) {
          reject(err);
        } else {
          resolve(deliSales);
        }
      });
    });
  }

  // Get operators who have sales data for a specific location
  getDailySalesOperators(locationId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT DISTINCT operator_name
        FROM (
          SELECT DISTINCT s.operator_name
          FROM sales s
          WHERE s.location_id = ?
            AND s.operator_name IS NOT NULL
            AND s.operator_name != ''
          UNION
          SELECT DISTINCT t.operator_name
          FROM tickets t
          WHERE t.location_id = ?
            AND t.operator_name IS NOT NULL
            AND t.operator_name != ''
        ) AS operators
        ORDER BY operator_name
      `;

      this.db.all(query, [locationId, locationId], (err, operators) => {
        if (err) {
          reject(err);
        } else {
          resolve(operators);
        }
      });
    });
  }

  // Get all sales (admin only)
  getAllSales() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT s.*, COUNT(si.id) as item_count_actual
        FROM sales s
        LEFT JOIN sales_items si ON s.sale_id = si.sale_id
        GROUP BY s.id
        ORDER BY s.sale_date DESC
      `;

      this.db.all(query, [], (err, sales) => {
        if (err) {
          reject(err);
        } else {
          resolve(sales);
        }
      });
    });
  }

  // Get sale details with items
  getSaleDetails(saleId) {
    return new Promise((resolve, reject) => {
      const saleQuery = `SELECT * FROM sales WHERE sale_id = ?`;
      const itemsQuery = `SELECT * FROM sales_items WHERE sale_id = ?`;

      this.db.get(saleQuery, [saleId], (err, sale) => {
        if (err) {
          reject(err);
          return;
        }

        if (!sale) {
          resolve(null);
          return;
        }

        this.db.all(itemsQuery, [saleId], (err, items) => {
          if (err) {
            reject(err);
          } else {
            resolve({ ...sale, items });
          }
        });
      });
    });
  }

  // Shift Management Methods

  // Helper function to determine shift type based on current time
  determineShiftType(currentTime = null) {
    const dayjs = require('dayjs');
    const now = currentTime || dayjs();
    const hour = now.hour();

    // Day shift: 6 AM to 6 PM (6-17)
    // Night shift: 6 PM to 6 AM next day (18-5)
    return (hour >= 6 && hour < 18) ? 'day' : 'night';
  }

  // Helper function to get scheduled shift times
  getScheduledShiftTimes(shiftType, currentDate = null) {
    const dayjs = require('dayjs');
    const baseDate = currentDate || dayjs();

    if (shiftType === 'day') {
      return {
        start: baseDate.hour(6).minute(0).second(0).millisecond(0),
        end: baseDate.hour(18).minute(0).second(0).millisecond(0)
      };
    } else {
      // Night shift
      const startTime = baseDate.hour(18).minute(0).second(0).millisecond(0);
      let endTime;

      // If current time is before 6 AM, the shift started yesterday at 6 PM
      if (baseDate.hour() < 6) {
        endTime = baseDate.hour(6).minute(0).second(0).millisecond(0);
      } else {
        // If current time is after 6 AM, the shift will end tomorrow at 6 AM
        endTime = baseDate.add(1, 'day').hour(6).minute(0).second(0).millisecond(0);
      }

      return {
        start: startTime,
        end: endTime
      };
    }
  }

  // Helper function to check for carryover from previous shift
  async getShiftCarryover(userId, locationId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT overtime_minutes, shift_id, shift_type
        FROM shifts
        WHERE user_id = ? AND location_id = ? AND overtime_minutes > 0
        ORDER BY created_at DESC
        LIMIT 1
      `;

      this.db.get(query, [userId, locationId], (err, result) => {
        if (err) {
          reject(err);
        } else {
          resolve(result || { overtime_minutes: 0, shift_id: null, shift_type: null });
        }
      });
    });
  }

  startShift(shiftData) {
    return new Promise(async (resolve, reject) => {
      if (!this.db) {
        console.error('Database connection not available');
        reject(new Error('Database connection not available'));
        return;
      }

      try {
        const dayjs = require('dayjs');
        const now = dayjs();

        // First, end any active shifts for this user at this location
        await this.endActiveShifts(shiftData.user_id, shiftData.location_id);

        // Determine shift type based on current time
        const shiftType = this.determineShiftType(now);

        // Get scheduled shift times
        const scheduledTimes = this.getScheduledShiftTimes(shiftType, now);

        // Check for carryover from previous shift
        const carryover = await this.getShiftCarryover(shiftData.user_id, shiftData.location_id);

        // Calculate elapsed time since actual shift start (when user logged in)
        const actualStartTime = now; // Shift starts when user logs in, not at scheduled time
        const elapsedMinutes = 0; // Always start with 0 elapsed time for new shift

        // Calculate remaining time (always start with 12 hours + carryover)
        const totalShiftMinutes = 12 * 60; // 12 hours
        const remainingMinutes = totalShiftMinutes + carryover.overtime_minutes;

        console.log(`🕐 Starting ${shiftType} shift:`);
        console.log(`  - Scheduled start: ${scheduledTimes.start.format('YYYY-MM-DD HH:mm:ss')} (reference only)`);
        console.log(`  - Scheduled end: ${scheduledTimes.end.format('YYYY-MM-DD HH:mm:ss')} (reference only)`);
        console.log(`  - Actual start (login time): ${now.format('YYYY-MM-DD HH:mm:ss')}`);
        console.log(`  - Actual end time: ${now.add(totalShiftMinutes + carryover.overtime_minutes, 'minute').format('YYYY-MM-DD HH:mm:ss')}`);
        console.log(`  - Elapsed minutes: ${elapsedMinutes} (always 0 for new shift)`);
        console.log(`  - Carryover minutes: ${carryover.overtime_minutes}`);
        console.log(`  - Remaining minutes: ${remainingMinutes} (12 hours + carryover)`);

        const query = `
          INSERT INTO shifts (
            shift_id, user_id, location_id, operator_name, location_name,
            shift_start_time, shift_end_time, shift_duration_hours,
            remaining_time_minutes, status, shift_type, scheduled_start_time,
            scheduled_end_time, overtime_minutes, carryover_minutes,
            previous_shift_id, login_time
          ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, ?, ?, ?, ?, ?, ?
          )
        `;

        // Calculate actual end time (12 hours from now + carryover)
        const actualEndTime = now.add(totalShiftMinutes + carryover.overtime_minutes, 'minute');

        const values = [
          shiftData.shift_id,
          shiftData.user_id,
          shiftData.location_id,
          shiftData.operator_name,
          shiftData.location_name,
          now.format('YYYY-MM-DD HH:mm:ss'), // Actual start time (when user logged in)
          actualEndTime.format('YYYY-MM-DD HH:mm:ss'), // Actual end time (12 hours + carryover from start)
          12, // Always 12 hours
          remainingMinutes,
          shiftType,
          scheduledTimes.start.format('YYYY-MM-DD HH:mm:ss'), // Scheduled start (6 AM or 6 PM)
          scheduledTimes.end.format('YYYY-MM-DD HH:mm:ss'), // Scheduled end (6 PM or 6 AM)
          0, // Initial overtime is 0
          carryover.overtime_minutes,
          carryover.shift_id,
          now.format('YYYY-MM-DD HH:mm:ss') // Login time
        ];

        const self = this;

        this.db.run(query, values, function(err) {
          if (err) {
            console.error('Error starting shift:', err);
            reject(err);
          } else {
            // Get the created shift to return accurate times
            const selectQuery = `SELECT * FROM shifts WHERE id = ?`;
            const shiftId = this.lastID;

            setTimeout(() => {
              self.db.get(selectQuery, [shiftId], (selectErr, shift) => {
                if (selectErr) {
                  console.error('Error retrieving created shift:', selectErr);
                  reject(selectErr);
                } else {
                  resolve({
                    id: shift.id,
                    shift_id: shift.shift_id,
                    start_time: shift.shift_start_time,
                    end_time: shift.shift_end_time,
                    remaining_minutes: shift.remaining_time_minutes,
                    shift_type: shift.shift_type,
                    scheduled_start_time: shift.scheduled_start_time,
                    scheduled_end_time: shift.scheduled_end_time,
                    carryover_minutes: shift.carryover_minutes,
                    login_time: shift.login_time
                  });
                }
              });
            }, 10);
          }
        });
      } catch (error) {
        console.error('Error in startShift:', error);
        reject(error);
      }
    });
  }

  endActiveShifts(userId, locationId) {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE shifts
        SET status = 'ended',
            actual_end_time = datetime(CURRENT_TIMESTAMP, 'localtime'),
            updated_at = datetime(CURRENT_TIMESTAMP, 'localtime')
        WHERE user_id = ? AND location_id = ? AND status = 'active'
      `;

      this.db.run(query, [userId, locationId], function(err) {
        if (err) {
          console.error('Error ending active shifts:', err);
          reject(err);
        } else {

          resolve(this.changes);
        }
      });
    });
  }

  getCurrentShift(userId, locationId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM shifts
        WHERE user_id = ? AND location_id = ? AND status = 'active'
        ORDER BY shift_start_time DESC
        LIMIT 1
      `;

      this.db.get(query, [userId, locationId], (err, shift) => {
        if (err) {
          console.error('Error getting current shift:', err);
          reject(err);
        } else if (!shift) {
          resolve(null);
        } else {
          const dayjs = require('dayjs');
          const now = dayjs();

          // Use actual shift start time (when user logged in), not scheduled time
          const actualStartTime = dayjs(shift.shift_start_time);
          const actualEndTime = dayjs(shift.shift_end_time);

          if (!actualStartTime.isValid() || !actualEndTime.isValid()) {
            console.error('❌ SHIFT: Invalid shift times in database');
            resolve(shift);
            return;
          }

          // Calculate elapsed time since actual shift start (when user logged in)
          const elapsedMinutes = Math.max(0, now.diff(actualStartTime, 'minute'));

          // Calculate total shift time (12 hours = 720 minutes)
          const totalShiftMinutes = 12 * 60;

          // Calculate remaining time including carryover
          const carryoverMinutes = shift.carryover_minutes || 0;
          const totalAvailableMinutes = totalShiftMinutes + carryoverMinutes;
          const remainingMinutes = Math.max(0, totalAvailableMinutes - elapsedMinutes);

          // Calculate overtime if user has been working longer than their allocated time
          const overtimeMinutes = Math.max(0, elapsedMinutes - totalAvailableMinutes);

          // Check if shift should be automatically ended (after allocated time is used up)
          const shouldAutoEnd = elapsedMinutes >= totalAvailableMinutes && overtimeMinutes > 0;

          // Update shift data
          const updatedShift = {
            ...shift,
            remaining_time_minutes: remainingMinutes,
            overtime_minutes: overtimeMinutes,
            elapsed_minutes: elapsedMinutes,
            should_auto_end: shouldAutoEnd,
            is_overtime: overtimeMinutes > 0
          };

          // Update the remaining time and overtime in database
          this.updateShiftTimes(shift.shift_id, remainingMinutes, overtimeMinutes).then(() => {
            resolve(updatedShift);
          }).catch(() => {
            // If update fails, still return shift with calculated times
            resolve(updatedShift);
          });
        }
      });
    });
  }

  updateShiftTimes(shiftId, remainingMinutes, overtimeMinutes = 0) {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE shifts
        SET remaining_time_minutes = ?,
            overtime_minutes = ?,
            updated_at = datetime(CURRENT_TIMESTAMP, 'localtime')
        WHERE shift_id = ? AND status = 'active'
      `;

      this.db.run(query, [remainingMinutes, overtimeMinutes, shiftId], function(err) {
        if (err) {
          console.error('Error updating shift times:', err);
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  // Legacy function for backward compatibility
  updateShiftRemainingTime(shiftId, remainingMinutes) {
    return this.updateShiftTimes(shiftId, remainingMinutes, 0);
  }

  // Force end shift after 12 hours and prepare carryover for next shift
  async forceEndShiftWithCarryover(shiftId, overtimeMinutes) {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE shifts
        SET status = 'ended',
            actual_end_time = datetime(CURRENT_TIMESTAMP, 'localtime'),
            overtime_minutes = ?,
            updated_at = datetime(CURRENT_TIMESTAMP, 'localtime')
        WHERE shift_id = ? AND status = 'active'
      `;

      this.db.run(query, [overtimeMinutes, shiftId], function(err) {
        if (err) {
          console.error('Error force ending shift:', err);
          reject(err);
        } else {
          console.log(`🔄 Force ended shift ${shiftId} with ${overtimeMinutes} overtime minutes`);
          resolve(this.changes > 0);
        }
      });
    });
  }

  // Check if any shifts need automatic transition
  async checkForAutomaticShiftTransitions() {
    return new Promise((resolve, reject) => {
      const dayjs = require('dayjs');
      const now = dayjs();

      const query = `
        SELECT shift_id, user_id, location_id, operator_name, location_name,
               scheduled_start_time, scheduled_end_time, overtime_minutes
        FROM shifts
        WHERE status = 'active'
        AND scheduled_start_time IS NOT NULL
        AND datetime('now', 'localtime') > datetime(scheduled_start_time, '+12 hours')
      `;

      this.db.all(query, [], async (err, shifts) => {
        if (err) {
          reject(err);
        } else {
          const transitionPromises = shifts.map(async (shift) => {
            const scheduledStart = dayjs(shift.scheduled_start_time);
            const elapsedMinutes = now.diff(scheduledStart, 'minute');
            const overtimeMinutes = Math.max(0, elapsedMinutes - (12 * 60));

            if (overtimeMinutes > 0) {
              console.log(`⏰ Auto-transitioning shift ${shift.shift_id} with ${overtimeMinutes} overtime minutes`);

              // End current shift with overtime
              await this.forceEndShiftWithCarryover(shift.shift_id, overtimeMinutes);

              return {
                user_id: shift.user_id,
                location_id: shift.location_id,
                operator_name: shift.operator_name,
                location_name: shift.location_name,
                carryover_minutes: overtimeMinutes,
                previous_shift_id: shift.shift_id
              };
            }
            return null;
          });

          const transitionData = await Promise.all(transitionPromises);
          resolve(transitionData.filter(data => data !== null));
        }
      });
    });
  }

  // Add theater ticket price column to locations table if it doesn't exist
  addTheaterTicketPriceColumn() {
    return new Promise((resolve, reject) => {
      // Check if column exists
      this.db.all("PRAGMA table_info(locations)", (err, columns) => {
        if (err) {
          reject(err);
          return;
        }

        const hasTheaterTicketPrice = columns.some(col => col.name === 'theater_ticket_price');

        if (!hasTheaterTicketPrice) {
          this.db.run("ALTER TABLE locations ADD COLUMN theater_ticket_price DECIMAL(10,2) DEFAULT 56.00", (err) => {
            if (err) {
              console.error('Error adding theater_ticket_price column:', err);
              reject(err);
            } else {
              console.log('✅ theater_ticket_price column added successfully');

              // Update existing locations with default price
              this.db.run(`
                UPDATE locations
                SET theater_ticket_price = 56.00
                WHERE theater_ticket_price IS NULL
              `, (updateErr) => {
                if (updateErr) {
                  console.error('Error updating existing locations with default theater ticket price:', updateErr);
                  reject(updateErr);
                } else {
                  console.log('✅ Existing locations updated with default theater ticket price ($56.00)');
                  resolve();
                }
              });
            }
          });
        } else {
          console.log('theater_ticket_price column already exists');
          resolve();
        }
      });
    });
  }

  // Add brand_name column to products table if it doesn't exist
  addBrandNameColumn() {
    return new Promise((resolve, reject) => {
      // Check if column exists
      this.db.all("PRAGMA table_info(products)", (err, columns) => {
        if (err) {
          reject(err);
          return;
        }

        const hasBrandName = columns.some(col => col.name === 'brand_name');

        if (!hasBrandName) {
          this.db.run("ALTER TABLE products ADD COLUMN brand_name TEXT", (err) => {
            if (err) {
              console.error('Error adding brand_name column:', err);
              reject(err);
            } else {
              console.log('✅ brand_name column added successfully to products table');
              resolve();
            }
          });
        } else {
          console.log('brand_name column already exists in products table');
          resolve();
        }
      });
    });
  }

  endShift(shiftId, shiftSummary = {}) {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE shifts
        SET status = 'ended',
            actual_end_time = datetime(CURRENT_TIMESTAMP, 'localtime'),
            total_sales = ?,
            total_transactions = ?,
            total_sale_sales = ?,
            total_sale_transactions = ?,
            total_theater_sales = ?,
            total_theater_transactions = ?,
            total_deli_sales = ?,
            total_deli_transactions = ?,
            updated_at = datetime(CURRENT_TIMESTAMP, 'localtime')
        WHERE shift_id = ? AND status = 'active'
      `;

      const values = [
        shiftSummary.total_sales || 0,
        shiftSummary.total_transactions || 0,
        shiftSummary.total_sale_sales || 0,
        shiftSummary.total_sale_transactions || 0,
        shiftSummary.total_theater_sales || 0,
        shiftSummary.total_theater_transactions || 0,
        shiftSummary.total_deli_sales || 0,
        shiftSummary.total_deli_transactions || 0,
        shiftId
      ];

      this.db.run(query, values, function(err) {
        if (err) {
          console.error('Error ending shift:', err);
          reject(err);
        } else {

          resolve(this.changes > 0);
        }
      });
    });
  }

  // Update shift totals when a sale is made
  updateShiftTotalsForSale(userId, locationId, saleAmount, saleType = 'sale') {
    return new Promise((resolve, reject) => {
      console.log(`🔍 SHIFT UPDATE: Attempting to update shift totals for user ${userId}, location ${locationId}, amount $${saleAmount}, type ${saleType}`);

      // First get the current active shift
      this.getCurrentShift(userId, locationId).then(shift => {
        if (!shift) {
          console.log(`❌ SHIFT UPDATE: No active shift found for user ${userId}, location ${locationId}`);
          console.log(`   This means shift totals will NOT be updated for this sale!`);
          resolve(false);
          return;
        }

        console.log(`✅ SHIFT UPDATE: Found active shift ${shift.shift_id} for user ${userId}`);
        console.log(`   Current totals: $${shift.total_sales || 0} sales, ${shift.total_transactions || 0} transactions`);

        // Determine which columns to update based on sale type
        let salesColumn, transactionsColumn;
        switch (saleType) {
          case 'theater':
            salesColumn = 'total_theater_sales';
            transactionsColumn = 'total_theater_transactions';
            break;
          case 'deli':
            salesColumn = 'total_deli_sales';
            transactionsColumn = 'total_deli_transactions';
            break;
          default:
            salesColumn = 'total_sale_sales';
            transactionsColumn = 'total_sale_transactions';
        }

        const query = `
          UPDATE shifts
          SET total_sales = total_sales + ?,
              total_transactions = total_transactions + 1,
              ${salesColumn} = COALESCE(${salesColumn}, 0) + ?,
              ${transactionsColumn} = COALESCE(${transactionsColumn}, 0) + 1,
              updated_at = datetime(CURRENT_TIMESTAMP, 'localtime')
          WHERE shift_id = ? AND status = 'active'
        `;

        this.db.run(query, [saleAmount, saleAmount, shift.shift_id], function(err) {
          if (err) {
            console.error('❌ SHIFT UPDATE: Error updating shift totals:', err);
            reject(err);
          } else {
            console.log(`✅ SHIFT UPDATE: Successfully updated shift ${shift.shift_id}`);
            console.log(`   Added: $${saleAmount} (${saleType}), Rows affected: ${this.changes}`);
            resolve(this.changes > 0);
          }
        });
      }).catch(reject);
    });
  }

  getShiftHistory(locationId, limit = 50) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM shifts
        WHERE location_id = ?
        ORDER BY shift_start_time DESC
        LIMIT ?
      `;

      this.db.all(query, [locationId, limit], (err, shifts) => {
        if (err) {
          console.error('Error getting shift history:', err);
          reject(err);
        } else {
          resolve(shifts);
        }
      });
    });
  }

  // Recalculate shift totals from sales history
  recalculateShiftTotals(shiftId) {
    return new Promise((resolve, reject) => {
      // Get shift details
      const shiftQuery = `
        SELECT shift_id, user_id, location_id, shift_start_time,
               COALESCE(actual_end_time, shift_end_time) as end_time
        FROM shifts
        WHERE shift_id = ?
      `;

      this.db.get(shiftQuery, [shiftId], (err, shift) => {
        if (err) {
          reject(err);
          return;
        }

        if (!shift) {
          reject(new Error('Shift not found'));
          return;
        }

        // Calculate totals from sales during this shift
        const salesQuery = `
          SELECT
            sale_type,
            COUNT(*) as transaction_count,
            SUM(total_amount) as total_sales
          FROM sales
          WHERE user_id = ?
            AND location_id = ?
            AND sale_date >= ?
            AND sale_date <= ?
            AND status = 'completed'
          GROUP BY sale_type
        `;

        this.db.all(salesQuery, [
          shift.user_id,
          shift.location_id,
          shift.shift_start_time,
          shift.end_time
        ], (err, salesData) => {
          if (err) {
            reject(err);
            return;
          }

          // Initialize totals
          let totals = {
            total_sales: 0,
            total_transactions: 0,
            total_sale_sales: 0,
            total_sale_transactions: 0,
            total_theater_sales: 0,
            total_theater_transactions: 0,
            total_deli_sales: 0,
            total_deli_transactions: 0
          };

          // Process sales data
          salesData.forEach(row => {
            const amount = parseFloat(row.total_sales) || 0;
            const count = parseInt(row.transaction_count) || 0;

            totals.total_sales += amount;
            totals.total_transactions += count;

            switch (row.sale_type) {
              case 'theater':
                totals.total_theater_sales += amount;
                totals.total_theater_transactions += count;
                break;
              case 'deli':
                totals.total_deli_sales += amount;
                totals.total_deli_transactions += count;
                break;
              default:
                totals.total_sale_sales += amount;
                totals.total_sale_transactions += count;
            }
          });

          // Update shift with calculated totals
          const updateQuery = `
            UPDATE shifts
            SET total_sales = ?,
                total_transactions = ?,
                total_sale_sales = ?,
                total_sale_transactions = ?,
                total_theater_sales = ?,
                total_theater_transactions = ?,
                total_deli_sales = ?,
                total_deli_transactions = ?,
                updated_at = datetime(CURRENT_TIMESTAMP, 'localtime')
            WHERE shift_id = ?
          `;

          this.db.run(updateQuery, [
            totals.total_sales,
            totals.total_transactions,
            totals.total_sale_sales,
            totals.total_sale_transactions,
            totals.total_theater_sales,
            totals.total_theater_transactions,
            totals.total_deli_sales,
            totals.total_deli_transactions,
            shiftId
          ], function(err) {
            if (err) {
              reject(err);
            } else {
              console.log(`✅ Shift ${shiftId} totals recalculated:`, totals);
              resolve(totals);
            }
          });
        });
      });
    });
  }

  // Recalculate all shift totals from sales history
  recalculateAllShiftTotals() {
    return new Promise((resolve, reject) => {
      const query = `SELECT shift_id FROM shifts ORDER BY shift_start_time DESC`;

      this.db.all(query, [], (err, shifts) => {
        if (err) {
          reject(err);
          return;
        }

        if (shifts.length === 0) {
          resolve([]);
          return;
        }

        const promises = shifts.map(shift => this.recalculateShiftTotals(shift.shift_id));

        Promise.all(promises).then(results => {
          console.log(`✅ Recalculated totals for ${results.length} shifts`);
          resolve(results);
        }).catch(reject);
      });
    });
  }

  expireOldShifts() {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE shifts
        SET status = 'expired',
            updated_at = datetime(CURRENT_TIMESTAMP, 'localtime')
        WHERE status = 'active'
          AND datetime(shift_end_time) < datetime(CURRENT_TIMESTAMP, 'localtime')
      `;

      this.db.run(query, [], function(err) {
        if (err) {
          console.error('Error expiring old shifts:', err);
          reject(err);
        } else {

          resolve(this.changes);
        }
      });
    });
  }

  // Dashboard Statistics Functions
  getDailySalesSummary(date) {
    return new Promise((resolve, reject) => {
      const query = `
        WITH sales_data AS (
          SELECT
            COALESCE(SUM(s.total_amount), 0) as sales_total,
            COUNT(s.id) as sales_transactions,
            COALESCE(SUM(si.quantity), 0) as sales_items_sold
          FROM sales s
          LEFT JOIN sales_items si ON s.sale_id = si.sale_id
          WHERE DATE(s.sale_date) = ?
        ),
        tickets_data AS (
          SELECT
            COALESCE(SUM(t.total_amount), 0) as tickets_total,
            COUNT(t.id) as tickets_transactions,
            COUNT(t.id) as tickets_sold
          FROM tickets t
          WHERE DATE(t.issued_at) = ?
            AND t.status = 'active'
        )
        SELECT
          (sd.sales_total + td.tickets_total) as totalSales,
          (sd.sales_transactions + td.tickets_transactions) as totalTransactions,
          (sd.sales_items_sold + td.tickets_sold) as totalItemsSold
        FROM sales_data sd, tickets_data td
      `;

      this.db.get(query, [date, date], (err, result) => {
        if (err) {
          reject(err);
        } else {
          resolve(result || { totalSales: 0, totalTransactions: 0, totalItemsSold: 0 });
        }
      });
    });
  }

  getActiveUsersCount() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT COUNT(*) as count
        FROM users
        WHERE status = 'active'
      `;

      this.db.get(query, [], (err, result) => {
        if (err) {
          reject(err);
        } else {
          resolve(result ? result.count : 0);
        }
      });
    });
  }

  getRecentActivities(limit = 10) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT
          'sale' as type,
          'Regular sale completed - $' || PRINTF('%.2f', total_amount) || ' (' ||
          (SELECT COUNT(*) FROM sales_items WHERE sale_id = s.sale_id) || ' items)' as description,
          sale_date as created_at
        FROM sales s
        WHERE sale_date >= datetime('now', '-24 hours')

        UNION ALL

        SELECT
          'sale' as type,
          'Theater ticket issued - $' || PRINTF('%.2f', COALESCE(total_amount, 56.00)) as description,
          issued_at as created_at
        FROM tickets
        WHERE issued_at >= datetime('now', '-24 hours')
          AND status = 'active'

        UNION ALL

        SELECT
          'user' as type,
          'Shift started - ' || operator_name as description,
          shift_start_time as created_at
        FROM shifts
        WHERE shift_start_time >= datetime('now', '-24 hours')

        UNION ALL

        SELECT
          'user' as type,
          'Shift ended - ' || operator_name || ' (Duration: ' ||
          CASE
            WHEN actual_end_time IS NOT NULL THEN
              PRINTF('%.1f', (julianday(actual_end_time) - julianday(shift_start_time)) * 24) || 'h'
            ELSE 'ongoing'
          END || ')' as description,
          COALESCE(actual_end_time, shift_end_time) as created_at
        FROM shifts
        WHERE COALESCE(actual_end_time, shift_end_time) >= datetime('now', '-24 hours')
          AND actual_end_time IS NOT NULL

        ORDER BY created_at DESC
        LIMIT ?
      `;

      this.db.all(query, [limit], (err, activities) => {
        if (err) {
          reject(err);
        } else {
          resolve(activities || []);
        }
      });
    });
  }

  // Transaction Management Functions
  getTransactionSummary(date) {
    return new Promise((resolve, reject) => {
      const query = `
        WITH sales_summary AS (
          SELECT
            COUNT(s.id) as sales_count,
            COALESCE(SUM(s.total_amount), 0) as sales_total,
            COALESCE(SUM(si.quantity), 0) as sales_items
          FROM sales s
          LEFT JOIN sales_items si ON s.sale_id = si.sale_id
          WHERE DATE(s.sale_date) = ?
        ),
        tickets_summary AS (
          SELECT
            COUNT(t.id) as tickets_count,
            COALESCE(SUM(t.total_amount), 0) as tickets_total
          FROM tickets t
          WHERE DATE(t.issued_at) = ?
            AND t.status = 'active'
        ),
        refunds_summary AS (
          SELECT
            COUNT(t.id) as refunds_count
          FROM tickets t
          WHERE DATE(t.issued_at) = ?
            AND t.status = 'refunded'
        )
        SELECT
          (ss.sales_count + ts.tickets_count) as totalTransactions,
          (ss.sales_total + ts.tickets_total) as totalAmount,
          CASE
            WHEN (ss.sales_count + ts.tickets_count) > 0
            THEN (ss.sales_total + ts.tickets_total) / (ss.sales_count + ts.tickets_count)
            ELSE 0
          END as averageSale,
          rs.refunds_count as refunds
        FROM sales_summary ss, tickets_summary ts, refunds_summary rs
      `;

      this.db.get(query, [date, date, date], (err, result) => {
        if (err) {
          reject(err);
        } else {
          resolve(result || { totalTransactions: 0, totalAmount: 0, averageSale: 0, refunds: 0 });
        }
      });
    });
  }

  getRecentTransactions(limit = 10) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT
          'SALE-' || s.sale_id as transaction_id,
          s.total_amount as amount,
          s.sale_date as created_at,
          'Regular Sale' as customer_name,
          (SELECT COUNT(*) FROM sales_items WHERE sale_id = s.sale_id) as items_count,
          'Cash/Card' as payment_method,
          'Completed' as status
        FROM sales s
        WHERE s.sale_date >= datetime('now', '-7 days')

        UNION ALL

        SELECT
          'TICKET-' || t.ticket_number as transaction_id,
          t.total_amount as amount,
          t.issued_at as created_at,
          COALESCE(t.customer_name, 'Theater Customer') as customer_name,
          1 as items_count,
          'Cash/Card' as payment_method,
          CASE
            WHEN t.status = 'active' THEN 'Completed'
            WHEN t.status = 'refunded' THEN 'Refunded'
            ELSE 'Pending'
          END as status
        FROM tickets t
        WHERE t.issued_at >= datetime('now', '-7 days')

        ORDER BY created_at DESC
        LIMIT ?
      `;

      this.db.all(query, [limit], (err, transactions) => {
        if (err) {
          reject(err);
        } else {
          resolve(transactions || []);
        }
      });
    });
  }

  /**
   * Validate stock consistency between products.max_qty and location_stocks.stock
   * This is a utility function to ensure data integrity
   */
  async validateStockConsistency(productId = null) {
    return new Promise((resolve, reject) => {
      let query = `
        SELECT
          p.id,
          p.barcode,
          p.description,
          p.max_qty,
          SUM(COALESCE(ls.stock, 0)) as total_location_stock,
          COUNT(ls.id) as location_count
        FROM products p
        LEFT JOIN location_stocks ls ON p.id = ls.product_id
      `;

      const params = [];
      if (productId) {
        query += ' WHERE p.id = ?';
        params.push(productId);
      }

      query += ' GROUP BY p.id, p.barcode, p.description, p.max_qty';

      this.db.all(query, params, (err, results) => {
        if (err) {
          reject(err);
        } else {
          const inconsistencies = results.filter(row =>
            row.max_qty !== row.total_location_stock
          );

          resolve({
            total_products: results.length,
            inconsistencies: inconsistencies.length,
            details: inconsistencies.map(row => ({
              productId: row.id,
              barcode: row.barcode,
              description: row.description,
              global_stock: row.max_qty,
              location_stock_sum: row.total_location_stock,
              difference: row.max_qty - row.total_location_stock,
              location_count: row.location_count
            }))
          });
        }
      });
    });
  }

  /**
   * Get detailed stock information for a product
   */
  async getProductStockDetails(productId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT
          p.id,
          p.barcode,
          p.description,
          p.max_qty as global_stock,
          p.min_qty as global_min_qty,
          ls.location,
          COALESCE(ls.stock, 0) as location_stock,
          COALESCE(ls.price, 0) as price
        FROM products p
        LEFT JOIN location_stocks ls ON p.id = ls.product_id
        WHERE p.id = ?
        ORDER BY ls.location
      `;

      this.db.all(query, [productId], (err, results) => {
        if (err) {
          reject(err);
        } else if (results.length === 0) {
          reject(new Error(`Product with ID ${productId} not found`));
        } else {
          const product = {
            id: results[0].id,
            barcode: results[0].barcode,
            description: results[0].description,
            global_stock: results[0].global_stock,
            global_min_qty: results[0].global_min_qty,
            locations: results.map(row => ({
              location: row.location,
              stock: row.location_stock,
              price: row.price
            })).filter(loc => loc.location) // Filter out null locations
          };

          resolve(product);
        }
      });
    });
  }

  close() {
    if (this.db) {
      this.db.close();
    }
  }
}

module.exports = Database;
