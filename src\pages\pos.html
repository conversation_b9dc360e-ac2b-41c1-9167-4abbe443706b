<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS System - Rainbow Station Inc</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #000000;
            color: #00ff00;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            overflow: hidden;
        }

        .pos-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: 0;
        }

        /* Header */
        .header {
            background-color: #000000;
            border: 1px solid #666666;
            padding: 8px 8px;
            margin-bottom: 0;
            flex-shrink: 0;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 16px;
        }

        .header-left-section {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            flex: 1;
        }

        .header-center-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            text-align: center;
        }

        .header-right-section {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            flex: 1;
        }

        .header-buttons-row {
            display: flex;
            gap: 8px;
            margin-bottom: 4px;
        }

        .header-datetime-row {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #00ff00;
        }

        .header-title h1 {
            font-size: 24px;
            font-weight: 900;
            color: #00ff00;
            margin: 0;
            margin-bottom: 4px;
        }

        .header-title {
            margin-top: 0;
            padding-top: 0;
        }

        .header-title p {
            font-size: 18px;
            font-weight: bold;
            color: #00cc00;
            margin: 0;
            margin-bottom: 8px;
        }

        .operator-info {
            font-size: 14px;
            font-weight: bold;
            color: #00ff00;
            margin-bottom: 2px;
        }

        .shift-info {
            font-size: 14px;
            font-weight: bold;
            color: #00ff00;
        }

        .header-info {
            display: flex;
            align-items: center;
            gap: 16px;
            min-width: 0;
            flex: 1;
            justify-content: flex-end;
        }

        .header-datetime-row {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 24px;
            margin-bottom: 4px;
        }

        .header-datetime-row span {
            font-size: 14px;
            font-weight: bold;
            white-space: nowrap;
        }

        .time-info {
            text-align: right;
            min-width: 0;
            flex: 1;
        }

        .time-info div {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .header-row {
            display: flex;
            align-items: center;
            gap: 24px;
            margin-bottom: 8px;
            flex-wrap: wrap;
        }

        .header-row span {
            font-size: 16px;
            font-weight: bold;
            white-space: nowrap;
        }

        .time-info span {
            background-color: #333333;
            padding: 8px 12px;
            font-size: 20px;
            font-weight: 900;
            border-radius: 0;
        }

        .location-security-indicator {
            display: flex;
            align-items: center;
            padding: 4px 8px;
            background-color: rgba(16, 185, 129, 0.2);
            border: 1px solid #10b981;
            border-radius: 0;
            font-size: 12px;
            color: #10b981;
            font-weight: 600;
            margin-top: 4px;
            width: fit-content;
        }

        .location-security-indicator.warning {
            background-color: rgba(245, 158, 11, 0.2);
            border-color: #f59e0b;
            color: #f59e0b;
        }

        .location-security-indicator.error {
            background-color: rgba(239, 68, 68, 0.2);
            border-color: #ef4444;
            color: #ef4444;
        }

        /* Operator and Shift Row */
        .operator-shift-row {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        /* Shift Timer Styles */
        .shift-timer-inline {
            color: #00cc00;
            font-weight: 600;
        }

        .shift-timer {
            font-size: 18px;
            font-weight: 900;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            letter-spacing: 1px;
            text-shadow: 0 0 8px #00ff00;
            margin-left: 4px;
        }

        .shift-timer.warning {
            color: #ff9500;
            text-shadow: 0 0 8px #ff9500;
            animation: pulse-warning 1s infinite;
        }

        .shift-timer.expired {
            color: #ff0000;
            text-shadow: 0 0 8px #ff0000;
            animation: pulse-danger 0.5s infinite;
        }

        .shift-timer.overtime {
            color: #ff6600;
            text-shadow: 0 0 8px #ff6600;
            animation: pulse-warning 1s infinite;
        }

        .shift-type {
            font-size: 14px;
            font-weight: 700;
            color: #00ccff;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .shift-elapsed {
            font-size: 12px;
            color: #cccccc;
            font-family: 'Courier New', monospace;
        }

        .shift-warning {
            font-size: 11px;
            color: #ff9500;
            margin-top: 4px;
            font-weight: bold;
            animation: blink 1s infinite;
            text-align: center;
        }

        @keyframes pulse-warning {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes pulse-danger {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0; }
        }

        .header-buttons {
            display: flex;
            gap: 8px;
        }

        .btn {
            background-color: #000000;
            color: #ff0000;
            border: 1px solid #00ff00;
            font-size: 20px;
            font-weight: 900;
            padding: 12px 24px;
            cursor: pointer;
            border-radius: 0;
        }

        .btn:hover {
            background-color: #004400;
            border-color: #00ff00;
        }

        /* Main Content */
        .main-content {
            display: flex;
            gap: 0;
            flex: 1;
            overflow: hidden;
        }

        .left-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: calc(100vw - 320px);
        }

        /* Transaction Table */
        .transaction-table {
            background-color: #000000;
            border: 1px solid #666666;
            margin-bottom: 0;
            flex: 1;
            overflow-y: auto;
        }

        .transaction-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .transaction-table thead {
            position: sticky;
            top: 0;
            background-color: #000000;
        }

        .transaction-table th {
            color: #ff0000;
            padding: 12px;
            text-align: left;
            font-size: 20px;
            font-weight: 900;
            border-bottom: 1px solid #666666;
        }

        .transaction-table td {
            padding: 12px;
            color: #00ff00;
            font-size: 20px;
            font-weight: bold;
            border-bottom: 1px solid #333333;
        }

        .transaction-table tr {
            cursor: pointer;
        }

        .transaction-table tr:hover {
            background-color: #333333;
        }

        .transaction-table tr.selected {
            background-color: #333333;
        }

        .empty-cart {
            padding: 32px;
            text-align: center;
            color: #666666;
            font-size: 20px;
        }

        /* Bottom Sections */
        .bottom-sections {
            margin-top: auto;
            display: flex;
            flex-direction: column;
            gap: 0;
        }

        .discount-totals {
            display: flex;
            gap: 0;
        }

        .discount-section {
            flex: 1;
            background-color: #1a1a1a;
            border: 1px solid #666666;
            border-radius: 8px;
            padding: 16px;
        }

        .totals-section {
            flex: 1;
            background-color: #1a1a1a;
            border: 1px solid #666666;
            border-radius: 8px;
            padding: 12px 16px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .discount-btn {
            background-color: #000000;
            color: #ff0000;
            border: 1px solid #ff0000;
            font-size: 18px;
            font-weight: 900;
            padding: 12px 24px;
            width: 100%;
            margin-bottom: 12px;
            cursor: pointer;
            border-radius: 4px;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-top: 8px;
        }

        .checkbox {
            width: 20px;
            height: 20px;
            border: 1px solid #00ff00;
        }

        .totals-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            margin-bottom: 8px;
        }

        .totals-row.border-top {
            border-top: 1px solid #666666;
            padding-top: 8px;
        }

        .totals-label {
            color: #00ff00;
            font-size: 16px;
            font-weight: bold;
        }

        .totals-value {
            color: #00ff00;
            font-size: 16px;
            font-weight: 900;
            background-color: #333333;
            padding: 4px 12px;
            border-radius: 4px;
            min-width: 80px;
            text-align: right;
        }

        .totals-label-large {
            color: #00ff00;
            font-size: 20px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .totals-value-large {
            color: #00ff00;
            font-size: 22px;
            font-weight: 900;
            background-color: #333333;
            padding: 8px 16px;
            border-radius: 6px;
            min-width: 100px;
            text-align: right;
            border: 2px solid #00ff00;
        }

        .totals-row-inline {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2px 0;
            margin-bottom: 6px;
        }

        .totals-item-inline {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex: 1;
        }

        .totals-item-inline:first-child {
            margin-right: 16px;
        }

        .discount-checkboxes {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-top: 8px;
        }

        .discount-checkbox-label {
            color: #00ff00;
            font-size: 14px;
            font-weight: bold;
            margin-left: 8px;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
        }

        .checkbox {
            width: 18px;
            height: 18px;
            accent-color: #00ff00;
        }

        /* Payment Section */
        .payment-section {
            display: flex;
            gap: 0;
        }

        .item-count {
            background-color: #000000;
            border: 2px solid #666666;
            padding: 24px;
            text-align: center;
            min-width: 160px;
            border-radius: 0;
        }

        .item-count-number {
            font-size: 72px;
            font-weight: 900;
            color: #00ff00;
        }

        .item-count-label {
            font-size: 14px;
            color: #00cc00;
            margin-top: 4px;
        }

        .cash-change {
            background-color: #000000;
            border: 2px solid #666666;
            padding: 24px;
            flex: 0.6;
            border-radius: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 16px;
        }

        .cash-change-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .cash-change-label {
            color: #00ff00;
            font-size: 24px;
            font-weight: bold;
        }

        .cash-change-value {
            font-size: 40px;
            font-weight: 900;
            color: #00ff00;
            background-color: #333333;
            padding: 12px 16px;
            border-radius: 0;
            min-width: 100px;
            text-align: center;
        }

        .total-amount {
            background-color: #000000;
            border: 2px solid #666666;
            padding: 24px;
            text-align: center;
            flex: 1;
            border-radius: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .total-label {
            font-size: 18px;
            color: #00cc00;
            margin-bottom: 12px;
            font-weight: bold;
        }

        .total-value {
            font-size: 96px;
            font-weight: 900;
            color: #00ff00;
            line-height: 1;
        }

        .checkout-button {
            background-color: #000000;
            border: 2px solid #666666;
            padding: 24px;
            min-width: 140px;
            border-radius: 8px;
        }

        .checkout-btn {
            background-color: #000000;
            color: #ff0000;
            border: 2px solid #ff0000;
            width: 100%;
            height: 100%;
            font-size: 24px;
            font-weight: 900;
            cursor: pointer;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 16px 20px;
            box-sizing: border-box;
            min-height: 80px;
        }

        .checkout-btn:hover {
            background-color: #440000;
            border-color: #ff4444;
        }

        .checkout-emoji {
            font-size: 32px;
        }

        /* Right Panel */
        .right-panel {
            width: 384px;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        /* Display */
        .display {
            background-color: #000000;
            border: 1px solid #666666;
            padding: 12px;
            margin-bottom: 4px;
            height: 56px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .display-text {
            font-size: 24px;
            font-weight: 900;
            color: #00ff00;
            text-align: center;
        }

        .price-indicator {
            color: #ff0000;
            font-size: 14px;
            margin-left: 8px;
        }

        /* Keypad */
        .keypad {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 4px;
            margin-bottom: 8px;
        }

        .keypad-btn {
            background-color: #000000;
            border: 2px solid #00ff00;
            color: #00ff00;
            font-size: 32px;
            font-weight: 900;
            height: 56px;
            cursor: pointer;
        }

        .keypad-btn:hover {
            background-color: #003300;
            border-color: #00ff00;
        }

        /* Control Buttons */
        .control-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 4px;
            margin-bottom: 8px;
        }

        .control-btn {
            background-color: #000000;
            border: 2px solid #00ff00;
            color: #00ff00;
            font-size: 20px;
            font-weight: 900;
            height: 56px;
            cursor: pointer;
        }

        .control-btn:hover {
            background-color: #003300;
            border-color: #00ff00;
        }

        .return-btn {
            background-color: #000000;
            color: #ff0000;
            border: 2px solid #ff0000;
            font-size: 20px;
            font-weight: 900;
            height: 56px;
            width: 100%;
            margin-bottom: 8px;
            cursor: pointer;
        }

        /* Function Buttons */
        .function-buttons {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 4px;
            flex: 1;
        }

        .function-btn {
            background-color: #000000;
            border: 2px solid;
            font-size: 16px;
            font-weight: 900;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 8px 4px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
        }

        .function-btn-icon {
            width: 36px;
            height: 36px;
            flex-shrink: 0;
        }

        .function-btn-text {
            display: flex;
            flex-direction: column;
            align-items: center;
            line-height: 1.1;
            font-size: 14px;
        }

        .function-btn.red {
            color: #ef4444;
            border-color: #ef4444;
        }

        .function-btn.blue {
            color: #3b82f6;
            border-color: #3b82f6;
        }

        .function-btn.green {
            color: #10b981;
            border-color: #10b981;
        }

        .function-btn:hover {
            background-color: #222222;
            border-color: #ffffff;
        }

        .function-btn.red:hover {
            background-color: #440000;
            border-color: #ff6666;
        }

        .function-btn.blue:hover {
            background-color: #001144;
            border-color: #6699ff;
        }

        .function-btn.green:hover {
            background-color: #004400;
            border-color: #66ff66;
        }

        .quick-action-wide {
            grid-column: span 3;
        }

        .checkout-function-btn {
            grid-column: span 3;
        }

        /* Quick Action button in payment section */
        .payment-section .quick-action-wide {
            min-width: 220px;
            flex: 0 0 220px;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 1000;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: #000000;
            border: 2px solid #00ff00;
            color: #00ff00;
            width: 98vw;
            height: 95vh;
            max-width: 1400px;
            max-height: 95vh;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            position: relative;
            margin: auto;
        }

        .modal-header {
            flex-shrink: 0;
            padding: 24px;
            border-bottom: 2px solid #666666;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 40px;
            font-weight: 900;
            color: #00ff00;
        }

        .close-btn {
            background-color: #000000;
            color: #ff0000;
            border: 2px solid #ff0000;
            padding: 12px;
            cursor: pointer;
        }

        .close-btn:hover {
            background-color: #440000;
            border-color: #ff6666;
        }

        /* Quick Action Modal Styles */
        .quick-action-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .quick-action-modal.show {
            display: flex;
        }

        .quick-action-modal-content {
            background-color: #ffffff;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow: hidden;
            position: relative;

        }



        .quick-action-modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px 32px;
            text-align: center;
        }

        .quick-action-modal-title {
            font-size: 28px;
            font-weight: 700;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .quick-action-modal-body {
            padding: 32px;
            background-color: #ffffff;
        }

        .quick-action-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .quick-action-grid .quick-action-btn:nth-child(5) {
            grid-column: 1 / -1;
            max-width: 95%;
            margin: 0 auto;
        }

        .quick-action-btn {
            background-color: #1f2937;
            color: #ffffff;
            border: 2px solid #374151;
            border-radius: 12px;
            padding: 20px 16px;
            font-size: 16px;
            font-weight: 600;
            text-transform: uppercase;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            min-height: 80px;
            letter-spacing: 0.5px;
        }

        .quick-action-btn:hover {
            background-color: #4a5568;
            border-color: #718096;
        }

        .quick-action-btn:active {
            background-color: #374151;
        }

        .quick-action-close-btn {
            background-color: #dc2626;
            color: #ffffff;
            border: 2px solid #dc2626;
            border-radius: 12px;
            padding: 16px 32px;
            font-size: 18px;
            font-weight: 700;
            text-transform: uppercase;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            width: 100%;
            letter-spacing: 1px;
        }

        .quick-action-close-btn:hover {
            background-color: #dc2626;
            border-color: #f87171;
        }

        .quick-action-close-btn:active {
            background-color: #b91c1c;
        }

        /* Report Modal Specific Styles */
        .report-options {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .date-selection {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .date-selection label {
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }

        .date-selection input[type="date"] {
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;

        }

        .date-selection input[type="date"]:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .shift-info {
            background: transparent;
            padding: 0;
            border: none;
        }

        .shift-info p {
            margin: 0;
            color: #00ff00;
            font-size: 14px;
        }

        .shift-info strong {
            color: #00ff00;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
        }

        .report-action-btn {
            padding: 16px 20px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            min-height: 60px;
        }

        .report-action-btn.email-btn {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
        }

        .report-action-btn.email-btn:hover {
            background: linear-gradient(135deg, #16a34a, #15803d);
        }

        .report-action-btn.print-btn {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
        }

        .report-action-btn.print-btn:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
        }

        .report-action-btn.view-btn {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
        }

        .report-action-btn.view-btn:hover {
            background: linear-gradient(135deg, #7c3aed, #6d28d9);
        }

        .report-action-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        /* Responsive header adjustments */
        @media (max-width: 1200px) {
            .header-datetime-row {
                gap: 16px;
            }

            .header-datetime-row span {
                font-size: 12px;
            }

            .header-main-row {
                flex-direction: column;
                gap: 8px;
            }

            .header-operator-section {
                margin-top: 4px;
                align-self: flex-end;
            }
        }

        @media (max-width: 900px) {
            .header-datetime-row {
                gap: 12px;
                flex-direction: column;
                align-items: flex-end;
            }

            .header-datetime-row span {
                font-size: 14px;
            }

            .header-buttons {
                gap: 4px;
            }

            .btn {
                padding: 6px 8px;
                font-size: 12px;
            }
        }

        /* Responsive styles for quick action modal */
        @media (max-width: 768px) {
            .quick-action-modal-content {
                width: 95%;
                margin: 20px;
            }

            .quick-action-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .quick-action-modal-header {
                padding: 20px 24px;
            }

            .quick-action-modal-title {
                font-size: 24px;
            }

            .quick-action-modal-body {
                padding: 24px;
            }

            .quick-action-btn {
                padding: 16px 12px;
                font-size: 14px;
                min-height: 70px;
            }
        }

        @media (max-width: 480px) {
            .quick-action-modal-content {
                width: 98%;
                margin: 10px;
            }

            .quick-action-modal-header {
                padding: 16px 20px;
            }

            .quick-action-modal-title {
                font-size: 20px;
            }

            .quick-action-modal-body {
                padding: 20px;
            }

            .quick-action-btn {
                padding: 14px 10px;
                font-size: 13px;
                min-height: 60px;
            }

            .action-buttons {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .report-action-btn {
                padding: 14px 16px;
                font-size: 14px;
                min-height: 50px;
            }

            .date-selection input[type="date"] {
                padding: 10px 12px;
                font-size: 14px;
            }

            .shift-info {
                padding: 12px;
            }

            .shift-info p {
                font-size: 13px;
            }
        }

        /* Responsive styles for checkout modal */
        @media (max-width: 1200px) {
            .modal-content {
                width: 99vw;
                height: 98vh;
            }
        }

        @media (max-width: 768px) {
            .modal-content {
                width: 100vw;
                height: 100vh;
                border-radius: 0;
            }

            .modal-title {
                font-size: 28px !important;
            }

            /* Stack layout on mobile */
            .checkout-main-content {
                flex-direction: column !important;
                gap: 8px !important;
                padding: 8px !important;
            }

            .checkout-left-panel {
                min-width: auto !important;
            }

            .checkout-right-panel {
                width: 100% !important;
                flex-direction: row !important;
                gap: 8px !important;
            }

            .checkout-keypad {
                max-width: none !important;
                grid-template-columns: repeat(4, 1fr) !important;
                gap: 6px !important;
            }

            .checkout-keypad button {
                font-size: 24px !important;
                padding: 12px !important;
            }

            .checkout-payment-methods {
                max-width: none !important;
            }

            .checkout-payment-methods > div:first-child {
                grid-template-columns: 1fr !important;
                gap: 6px !important;
            }

            .checkout-payment-methods > div:last-child {
                grid-template-columns: 1fr !important;
                gap: 6px !important;
            }

            .checkout-payment-row {
                padding: 8px !important;
            }

            .checkout-payment-text {
                font-size: 18px !important;
            }

            .checkout-payment-amount {
                font-size: 20px !important;
            }

            .checkout-summary-item {
                padding: 10px !important;
            }

            .checkout-summary-value {
                font-size: 36px !important;
            }

            #payment-input {
                font-size: 20px !important;
            }

            /* Responsive checkout button for tablets */
            .checkout-btn {
                padding: 14px 18px !important;
                font-size: 22px !important;
                min-height: 70px !important;
            }

            .checkout-emoji {
                font-size: 28px !important;
            }
        }

        @media (max-width: 480px) {
            .modal-title {
                font-size: 22px !important;
            }

            .checkout-main-content {
                padding: 6px !important;
                gap: 6px !important;
            }

            .checkout-keypad {
                grid-template-columns: repeat(3, 1fr) !important;
                gap: 4px !important;
                max-width: 280px !important;
            }

            .checkout-keypad button {
                font-size: 20px !important;
                padding: 10px !important;
            }

            .checkout-payment-methods > div:first-child {
                grid-template-columns: 1fr !important;
                gap: 4px !important;
            }

            .checkout-payment-methods > div:last-child {
                grid-template-columns: 1fr !important;
                gap: 4px !important;
            }

            .checkout-payment-row {
                padding: 8px !important;
                flex-wrap: wrap !important;
            }

            .checkout-payment-text {
                font-size: 18px !important;
            }

            .checkout-payment-amount {
                font-size: 18px !important;
            }

            .checkout-summary-value {
                font-size: 28px !important;
            }

            .checkout-summary-item {
                padding: 8px !important;
            }

            #payment-input {
                font-size: 18px !important;
                padding: 8px !important;
            }

            .checkout-right-panel {
                flex-direction: column !important;
            }
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .main-content {
                flex-direction: column;
            }
            
            .right-panel {
                width: 100%;
                order: -1;
                flex: 0 0 auto;
            }
            
            .left-panel {
                max-width: 100%;
            }
        }

        /* Discount Modal Styles */
        .discount-modal-content {
            background-color: #1a1a1a;
            border: 2px solid #00ff00;
            border-radius: 0;
            max-width: 700px;
            width: 80%;
            max-height: 300px;
            height: auto;
        }

        .discount-input-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 20px;
        }

        .discount-input-group {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .discount-input-group label {
            color: #00ff00;
            font-size: 18px;
            font-weight: bold;
        }

        .discount-input-group input {
            background-color: #000000;
            border: 2px solid #666666;
            border-radius: 0;
            color: #00ff00;
            font-size: 16px;
            font-weight: bold;
            padding: 10px;
            text-align: center;
            width: 100%;
        }

        .discount-input-group input:focus {
            outline: none;
            border-color: #00ff00;
        }

        .discount-apply-btn {
            background-color: #000000;
            border: 2px solid #00ff00;
            border-radius: 0;
            color: #00ff00;
            font-size: 16px;
            font-weight: bold;
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.2s;
            width: 100%;
        }

        .discount-apply-btn:hover {
            background-color: #00ff00;
            color: #000000;
        }

        .percentage-btn {
            border-color: #ff6600;
            color: #ff6600;
        }

        .percentage-btn:hover {
            background-color: #ff6600;
            color: #000000;
        }

        .fixed-rate-btn {
            border-color: #0066ff;
            color: #0066ff;
        }

        .fixed-rate-btn:hover {
            background-color: #0066ff;
            color: #000000;
        }
    </style>
</head>
<body>
    <div class="pos-container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <!-- Left Section: Title and Location -->
                <div class="header-left-section">
                    <div class="header-title">
                        <h1>POINT OF SALE SYSTEM</h1>
                        <p id="location-name">Rainbow Station Inc.</p>
                        <div class="location-security-indicator">
                            <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" style="margin-right: 4px;">
                                <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C15.4,11.5 16,12.4 16,13V16C16,17.4 15.4,18 14.8,18H9.2C8.6,18 8,17.4 8,16V13C8,12.4 8.6,11.5 9.2,11.5V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,10V11.5H13.5V10C13.5,8.7 12.8,8.2 12,8.2Z"/>
                            </svg>
                            Location Access: <span id="location-access-status">Secure</span>
                        </div>
                    </div>
                </div>

                <!-- Center Section: Operator and Shift Info -->
                <div class="header-center-section">
                    <div class="operator-info">Operator: <span id="current-operator">System Administrator</span></div>
                    <div class="shift-info" id="shift-timer-container">
                        <div id="shift-type" class="shift-type">DAY SHIFT</div>
                        <div>Remaining: <span id="shift-timer" class="shift-timer">11:51:00</span></div>
                        <div id="shift-elapsed" class="shift-elapsed">Elapsed: 00:09:00</div>
                    </div>
                    <div id="shift-warning" class="shift-warning" style="display: none;">
                        ⚠️ Shift ending in 30 minutes!
                    </div>
                </div>

                <!-- Right Section: Buttons and Date/Time -->
                <div class="header-right-section">
                    <div class="header-buttons-row">
                        <button class="btn" onclick="navigateToAdmin()">ADMIN</button>
                        <button class="btn" onclick="navigateToTheater()">THEATER</button>
                        <button class="btn" onclick="logout()">LOGOUT</button>
                        <button class="btn" onclick="toggleFullscreen()" id="fullscreen-btn" title="Maximize Window">⛶</button>
                        <button class="btn" onclick="minimizeApp()">−</button>
                        <button class="btn" onclick="closeApp()">×</button>
                    </div>
                    <div class="header-datetime-row">
                        <span>Date: <span id="current-date">07/30/25</span></span>
                        <span>Time: <span id="current-time">09:48:21</span></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <!-- Left Panel -->
            <div class="left-panel">
                <!-- Transaction Table -->
                <div class="transaction-table">
                    <table>
                        <thead>
                            <tr>
                                <th>QH</th>
                                <th>Description</th>
                                <th>Quantity</th>
                                <th>Price</th>
                                <th>Disc</th>
                                <th>Value</th>
                            </tr>
                        </thead>
                        <tbody id="cart-items">
                            <tr>
                                <td colspan="6" class="empty-cart">
                                    No items in cart. Click SELECT ITEM to add products.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Bottom Sections -->
                <div class="bottom-sections">
                    <!-- Discount and Totals -->
                    <div class="discount-totals">
                        <div class="discount-section">
                            <button class="discount-btn" onclick="openDiscountModal()">Add Discount</button>
                            <div class="discount-checkboxes">
                                <div class="checkbox-container">
                                    <input type="checkbox" class="checkbox" id="special-discount">
                                    <span class="discount-checkbox-label">Special Discount</span>
                                </div>
                            </div>
                        </div>
                        <div class="totals-section">
                            <div class="totals-row-inline">
                                <div class="totals-item-inline">
                                    <span class="totals-label-large">Sub total</span>
                                    <span class="totals-value-large" id="subtotal">0.00</span>
                                </div>
                                <div class="totals-item-inline">
                                    <span class="totals-label-large">Total Item Discount</span>
                                    <span class="totals-value-large" id="item-discount">0.00</span>
                                </div>
                            </div>
                            <div class="totals-row-inline">
                                <div class="totals-item-inline">
                                    <span class="totals-label-large">Tax 6.625%</span>
                                    <span class="totals-value-large" id="tax-amount">0.00</span>
                                </div>
                                <div class="totals-item-inline">
                                    <span class="totals-label-large">Discount</span>
                                    <span class="totals-value-large" id="discount-amount">0.00</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Section -->
                    <div class="payment-section">
                        <!-- Quick Action Button (moved from function buttons) -->
                        <button class="function-btn green quick-action-wide" onclick="showQuickActionModal()">
                            <svg class="function-btn-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"/>
                            </svg>
                            <div class="function-btn-text">
                                <span>QUICK</span>
                                <span>ACTION</span>
                            </div>
                        </button>
                        <div class="item-count">
                            <div class="item-count-number" id="item-count">0</div>
                            <div class="item-count-label">ITEMS</div>
                        </div>
                        <div class="cash-change">
                            <div class="cash-change-row">
                                <span class="cash-change-label">Cash</span>
                                <div class="cash-change-value" id="cash-amount">0.00</div>
                            </div>
                            <div class="cash-change-row">
                                <span class="cash-change-label">Change</span>
                                <div class="cash-change-value" id="change-amount">0.00</div>
                            </div>
                        </div>
                        <div class="total-amount">
                            <div class="total-label">TOTAL</div>
                            <div class="total-value" id="total-amount">0.00</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel -->
            <div class="right-panel">
                <!-- Display -->
                <div class="display">
                    <div class="display-text">
                        <span id="current-input">0</span>
                        <span class="price-indicator" id="price-indicator" style="display: none;">(PRICE)</span>
                        <span class="barcode-indicator" id="barcode-indicator" style="display: none; color: #00ccff; font-size: 12px; margin-left: 8px;">(SCAN BARCODE)</span>
                    </div>
                </div>

                <!-- Keypad -->
                <div class="keypad">
                    <button class="keypad-btn" onclick="handleNumberClick('1')">1</button>
                    <button class="keypad-btn" onclick="handleNumberClick('2')">2</button>
                    <button class="keypad-btn" onclick="handleNumberClick('3')">3</button>
                    <button class="keypad-btn" onclick="handleNumberClick('4')">4</button>
                    <button class="keypad-btn" onclick="handleNumberClick('5')">5</button>
                    <button class="keypad-btn" onclick="handleNumberClick('6')">6</button>
                    <button class="keypad-btn" onclick="handleNumberClick('7')">7</button>
                    <button class="keypad-btn" onclick="handleNumberClick('8')">8</button>
                    <button class="keypad-btn" onclick="handleNumberClick('9')">9</button>
                    <button class="keypad-btn" onclick="handleNumberClick('.')">.</button>
                    <button class="keypad-btn" onclick="handleNumberClick('0')">0</button>
                    <button class="keypad-btn" onclick="handleBackspace()">←</button>
                </div>

                <!-- Control Buttons -->
                <div class="control-buttons">
                    <button class="control-btn" onclick="handleClear()">CLEAR</button>
                    <button class="control-btn" onclick="handleEnter()" title="Press ENTER to add product by barcode">ENTER</button>
                </div>



                <button class="return-btn">RETURN/EXCHANGE</button>

                <!-- Function Buttons -->
                <div class="function-buttons">
                    <button class="function-btn red" onclick="showProductModal()">
                        <svg class="function-btn-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v10z"/>
                        </svg>
                        <div class="function-btn-text">
                            <span>SELECT</span>
                            <span>ITEM</span>
                        </div>
                    </button>
                    <button class="function-btn blue" onclick="handleHold()">
                        <svg class="function-btn-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                        <div class="function-btn-text">
                            <span>HOLD</span>
                        </div>
                    </button>
                    <button class="function-btn blue" onclick="handleRecall()">
                        <svg class="function-btn-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <div class="function-btn-text">
                            <span>RECALL</span>
                        </div>
                    </button>
                    <button class="function-btn red" onclick="handleEditPrice()">
                        <svg class="function-btn-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                        </svg>
                        <div class="function-btn-text">
                            <span>EDIT</span>
                            <span>PRICE</span>
                        </div>
                    </button>
                    <button class="function-btn red">
                        <svg class="function-btn-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/>
                        </svg>
                        <div class="function-btn-text">
                            <span>OPEN</span>
                            <span>DRAWER</span>
                        </div>
                    </button>
                    <button class="function-btn red" onclick="handleRemoveItem()">
                        <svg class="function-btn-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                        </svg>
                        <div class="function-btn-text">
                            <span>CANCEL</span>
                        </div>
                    </button>
                    <button class="function-btn red checkout-function-btn" onclick="handleCheckout()">
                        <svg class="function-btn-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                        </svg>
                        <div class="function-btn-text">
                            <span>CHECK</span>
                            <span>OUT</span>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Discount Modal -->
    <div id="discount-modal" class="modal" style="display: none;">
        <div class="modal-content discount-modal-content">
            <div class="modal-header">
                <h2>Add Discount</h2>
                <button class="close-btn" onclick="closeDiscountModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="discount-input-section">
                    <div class="discount-input-group">
                        <label for="percentage-input">Percentage (%)</label>
                        <input type="number" id="percentage-input" placeholder="Enter percentage" min="0" max="100" step="0.01">
                        <button class="discount-apply-btn percentage-btn" onclick="applyPercentageDiscount()">Percentage</button>
                    </div>
                    <div class="discount-input-group">
                        <label for="fixed-amount-input">Fixed Amount ($)</label>
                        <input type="number" id="fixed-amount-input" placeholder="Enter amount" min="0" step="0.01">
                        <button class="discount-apply-btn fixed-rate-btn" onclick="applyFixedDiscount()">Fixed Rate</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../node_modules/onscan.js/onscan.min.js"></script>
    <script src="../services/simpleBarcodeScanner.js"></script>
    <script src="../services/customerDisplay.js"></script>
    <script src="../services/cashDrawerService.js"></script>
    <script src="pos.js"></script>
</body>
</html>
