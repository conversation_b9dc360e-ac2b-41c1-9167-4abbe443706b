const SupabaseConfig = require('./src/supabase-config');

async function testSyncFix() {
    console.log('🧪 Testing Sync Fix for Foreign Key Constraint Violation\n');
    
    try {
        // Initialize Supabase config
        const supabaseConfig = new SupabaseConfig();
        
        console.log('🔄 Starting sync process...\n');
        
        // Run the sync
        const result = await supabaseConfig.syncAllTables();
        
        console.log('\n📊 Sync Results:');
        console.log('================');
        
        Object.entries(result).forEach(([table, tableResult]) => {
            const status = tableResult.success ? '✅' : '❌';
            console.log(`${status} ${table}: ${tableResult.message} (${tableResult.count || 0} records)`);
            
            if (!tableResult.success) {
                console.log(`   Error: ${tableResult.error}`);
            }
        });
        
        // Check if all syncs were successful
        const allSuccessful = Object.values(result).every(r => r.success);
        
        console.log('\n🎯 Overall Result:');
        console.log('==================');
        if (allSuccessful) {
            console.log('✅ All tables synced successfully!');
            console.log('🔧 Foreign key constraint violation has been fixed.');
        } else {
            console.log('❌ Some tables failed to sync.');
            console.log('🔍 Check the error messages above for details.');
        }
        
    } catch (error) {
        console.error('❌ Test failed with error:', error);
        console.error('Stack trace:', error.stack);
    }
}

// Run the test
testSyncFix().then(() => {
    console.log('\n🏁 Test completed.');
    process.exit(0);
}).catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
});
