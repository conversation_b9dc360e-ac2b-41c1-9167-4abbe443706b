const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Check both possible database locations
const dbPaths = [
    path.join(__dirname, 'pos_database.db'),
    path.join(__dirname, 'src', 'pos_system.db')
];

async function checkDatabase(dbPath) {
    return new Promise((resolve, reject) => {
        console.log(`\n🔍 Checking database: ${dbPath}`);
        
        const db = new sqlite3.Database(dbPath, (err) => {
            if (err) {
                console.log(`❌ Cannot open database: ${err.message}`);
                resolve(null);
                return;
            }
            
            console.log(`✅ Database opened successfully`);
            
            // Check if sales_items table exists and get data
            db.all(`
                SELECT name FROM sqlite_master WHERE type='table' AND name='sales_items'
            `, [], (err, tables) => {
                if (err) {
                    console.log(`❌ Error checking tables: ${err.message}`);
                    db.close();
                    resolve(null);
                    return;
                }
                
                if (tables.length === 0) {
                    console.log(`📭 No sales_items table found`);
                    db.close();
                    resolve(null);
                    return;
                }
                
                // Get sales_items data
                db.all(`
                    SELECT 
                        COUNT(*) as total_sales_items,
                        COUNT(DISTINCT product_id) as unique_product_ids
                    FROM sales_items
                `, [], (err, summary) => {
                    if (err) {
                        console.log(`❌ Error getting sales_items summary: ${err.message}`);
                        db.close();
                        resolve(null);
                        return;
                    }
                    
                    console.log(`📊 Sales Items Summary:`, summary[0]);
                    
                    // Get unique product_ids in sales_items
                    db.all(`
                        SELECT DISTINCT product_id, COUNT(*) as count
                        FROM sales_items 
                        WHERE product_id IS NOT NULL
                        GROUP BY product_id
                        ORDER BY product_id
                    `, [], (err, productIds) => {
                        if (err) {
                            console.log(`❌ Error getting product_ids: ${err.message}`);
                            db.close();
                            resolve(null);
                            return;
                        }
                        
                        console.log(`🔢 Product IDs in sales_items:`, productIds);
                        
                        // Get products table data
                        db.all(`
                            SELECT id, barcode, description
                            FROM products
                            ORDER BY id
                        `, [], (err, products) => {
                            if (err) {
                                console.log(`❌ Error getting products: ${err.message}`);
                                db.close();
                                resolve(null);
                                return;
                            }
                            
                            console.log(`📦 Products in local database:`, products);
                            
                            // Find orphaned sales_items
                            const productIdSet = new Set(products.map(p => p.id));
                            const orphanedProductIds = productIds.filter(item => !productIdSet.has(item.product_id));
                            
                            if (orphanedProductIds.length > 0) {
                                console.log(`⚠️ FOUND ORPHANED SALES_ITEMS:`, orphanedProductIds);
                                console.log(`These sales_items reference product_ids that don't exist in the products table!`);
                            } else {
                                console.log(`✅ All sales_items have valid product_id references`);
                            }
                            
                            db.close();
                            resolve({
                                dbPath,
                                salesItemsCount: summary[0].total_sales_items,
                                uniqueProductIds: summary[0].unique_product_ids,
                                productIds,
                                products,
                                orphanedProductIds
                            });
                        });
                    });
                });
            });
        });
    });
}

async function main() {
    console.log('🔍 Debugging Sync Issue - Foreign Key Constraint Violation');
    console.log('=========================================================');
    
    for (const dbPath of dbPaths) {
        const result = await checkDatabase(dbPath);
        if (result) {
            console.log(`\n✅ Found working database: ${result.dbPath}`);
            console.log(`📊 Summary:`);
            console.log(`   - Sales Items: ${result.salesItemsCount}`);
            console.log(`   - Unique Product IDs in sales_items: ${result.uniqueProductIds}`);
            console.log(`   - Products in local DB: ${result.products.length}`);
            console.log(`   - Orphaned sales_items: ${result.orphanedProductIds.length}`);
            
            if (result.orphanedProductIds.length > 0) {
                console.log(`\n🚨 ISSUE IDENTIFIED:`);
                console.log(`The sync is failing because there are sales_items with product_id values`);
                console.log(`that don't exist in the products table. This violates the foreign key constraint.`);
                console.log(`\n💡 SOLUTION:`);
                console.log(`The updated validation code should filter out these orphaned records.`);
            }
            
            return;
        }
    }
    
    console.log(`❌ No working database found in any of the checked locations.`);
}

main().catch(console.error);
