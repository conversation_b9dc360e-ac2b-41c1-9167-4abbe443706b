const sqlite3 = require('sqlite3').verbose();

const db = new sqlite3.Database('./src/pos_system.db', (err) => {
    if (err) {
        console.error('Error opening database:', err);
        return;
    }
    
    console.log('Connected to database');
    
    // Check tables
    db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {
        if (err) {
            console.error('Error getting tables:', err);
        } else {
            console.log('Tables:', tables.map(t => t.name));
            
            // Check if products table exists and has data
            if (tables.some(t => t.name === 'products')) {
                db.all("SELECT id, barcode, description, max_qty FROM products LIMIT 5", (err, products) => {
                    if (err) {
                        console.error('Error getting products:', err);
                    } else {
                        console.log('Sample products:', products);
                        
                        // Check location_stocks
                        db.all("SELECT * FROM location_stocks LIMIT 5", (err, stocks) => {
                            if (err) {
                                console.error('Error getting location stocks:', err);
                            } else {
                                console.log('Sample location stocks:', stocks);
                            }
                            db.close();
                        });
                    }
                });
            } else {
                console.log('Products table does not exist');
                db.close();
            }
        }
    });
});
