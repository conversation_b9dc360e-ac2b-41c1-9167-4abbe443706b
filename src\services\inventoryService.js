/**
 * Inventory Management Service
 * Handles stock level tracking, reorder alerts, and inventory updates
 */

class InventoryService {
    constructor(database) {
        this.db = database;
        this.reorderAlerts = [];
        this.lowStockThreshold = 5; // Default threshold for low stock warnings
        
        console.log('📦 Inventory Service initialized');
    }

    /**
     * Update inventory after a sale
     * Reduces stock levels for sold items in both location_stocks and products tables
     */
    async updateInventoryAfterSale(saleItems, locationName) {
        try {
            console.log(`📦 Updating inventory for ${saleItems.length} items at ${locationName}`);

            const allAlerts = [];
            const updateResults = [];

            // Process each item sequentially to maintain transaction integrity
            for (const item of saleItems) {
                if (item.productId && item.quantity > 0) {
                    try {
                        const result = await this.reduceStock(item.productId, item.quantity, locationName);
                        updateResults.push({
                            productId: item.productId,
                            productName: item.name,
                            quantity: item.quantity,
                            result: result
                        });

                        // Collect alerts from this item
                        if (result.alerts && result.alerts.length > 0) {
                            allAlerts.push(...result.alerts);
                        }

                        console.log(`✅ Updated stock for ${item.name}: -${item.quantity} units`);
                    } catch (itemError) {
                        console.error(`❌ Failed to update stock for ${item.name}:`, itemError.message);
                        // For individual item failures, we continue with other items but log the error
                        updateResults.push({
                            productId: item.productId,
                            productName: item.name,
                            quantity: item.quantity,
                            error: itemError.message
                        });
                    }
                } else {
                    console.warn(`⚠️ Skipping item with missing productId or invalid quantity:`, item);
                }
            }

            // Check for additional reorder alerts after inventory update
            await this.checkReorderLevels(locationName);

            console.log('✅ Inventory update completed');
            console.log(`   Items processed: ${updateResults.length}`);
            console.log(`   Alerts generated: ${allAlerts.length}`);

            return {
                success: true,
                results: updateResults,
                alerts: allAlerts,
                itemsProcessed: updateResults.length
            };

        } catch (error) {
            console.error('❌ Error updating inventory:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Reduce stock for a specific product at a location
     * Updates both location_stocks.stock and products.max_qty
     */
    async reduceStock(productId, quantity, locationName) {
        return new Promise((resolve, reject) => {
            // Validate input parameters
            if (!productId || !quantity || quantity <= 0 || !locationName) {
                const error = new Error('Invalid parameters: productId, positive quantity, and locationName are required');
                console.error('❌ Stock reduction validation failed:', error.message);
                reject(error);
                return;
            }

            // First check current stock levels
            const checkQuery = `
                SELECT ls.stock, p.description, p.max_qty, p.min_qty as product_min_qty
                FROM location_stocks ls
                JOIN products p ON ls.product_id = p.id
                WHERE ls.product_id = ? AND ls.location = ?
            `;

            this.db.db.get(checkQuery, [productId, locationName], (err, row) => {
                if (err) {
                    console.error(`❌ Error checking stock for product ${productId}:`, err);
                    reject(err);
                    return;
                }

                if (!row) {
                    console.warn(`⚠️ No stock record found for product ${productId} at ${locationName}`);
                    resolve({ changes: 0, warning: 'No stock record found' });
                    return;
                }

                // Validate sufficient stock
                if (row.stock < quantity) {
                    const error = new Error(`Insufficient stock: Available ${row.stock}, Requested ${quantity}`);
                    console.error(`❌ ${error.message} for product ${row.description}`);
                    reject(error);
                    return;
                }

                if (row.max_qty < quantity) {
                    const error = new Error(`Insufficient global stock: Available ${row.max_qty}, Requested ${quantity}`);
                    console.error(`❌ ${error.message} for product ${row.description}`);
                    reject(error);
                    return;
                }

                console.log(`📦 BEFORE SALE - Product: ${row.description}`);
                console.log(`   Location Stock: ${row.stock}, Global Stock: ${row.max_qty}`);
                console.log(`   Product Min Qty: ${row.product_min_qty}`);
                console.log(`   Reducing by: ${quantity}`);

                // Begin transaction for atomic updates
                this.db.db.run('BEGIN TRANSACTION', (beginErr) => {
                    if (beginErr) {
                        console.error('❌ Error starting transaction:', beginErr);
                        reject(beginErr);
                        return;
                    }

                    // Update location stock
                    const updateLocationQuery = `
                        UPDATE location_stocks
                        SET stock = stock - ?, updated_at = CURRENT_TIMESTAMP
                        WHERE product_id = ? AND location = ?
                    `;

                    this.db.db.run(updateLocationQuery, [quantity, productId, locationName], (locationErr) => {
                        if (locationErr) {
                            console.error(`❌ Error updating location stock for product ${productId}:`, locationErr);
                            // Rollback transaction
                            this.db.db.run('ROLLBACK', () => {
                                reject(locationErr);
                            });
                            return;
                        }

                        // Update global product stock (max_qty)
                        const updateProductQuery = `
                            UPDATE products
                            SET max_qty = max_qty - ?, updated_at = CURRENT_TIMESTAMP
                            WHERE id = ?
                        `;

                        this.db.db.run(updateProductQuery, [quantity, productId], (productErr) => {
                            if (productErr) {
                                console.error(`❌ Error updating product max_qty for product ${productId}:`, productErr);
                                // Rollback transaction
                                this.db.db.run('ROLLBACK', () => {
                                    reject(productErr);
                                });
                                return;
                            }

                            // Commit transaction
                            this.db.db.run('COMMIT', (commitErr) => {
                                if (commitErr) {
                                    console.error('❌ Error committing transaction:', commitErr);
                                    reject(commitErr);
                                    return;
                                }

                                const newLocationStock = row.stock - quantity;
                                const newGlobalStock = row.max_qty - quantity;

                                console.log(`📦 AFTER SALE - Product: ${row.description}`);
                                console.log(`   New Location Stock: ${newLocationStock}`);
                                console.log(`   New Global Stock: ${newGlobalStock}`);

                                // Check for reorder alerts (using product min_qty for both checks since location doesn't have min_qty)
                                const alerts = [];
                                if (newLocationStock <= (row.product_min_qty || 0)) {
                                    const alert = `🚨 LOCATION REORDER ALERT: Product ${row.description} at ${locationName} is at or below minimum quantity (${row.product_min_qty})!`;
                                    console.log(alert);
                                    alerts.push({ type: 'location', message: alert, level: 'critical' });
                                }

                                if (newGlobalStock <= (row.product_min_qty || 0)) {
                                    const alert = `🚨 GLOBAL REORDER ALERT: Product ${row.description} global stock is at or below minimum quantity (${row.product_min_qty})!`;
                                    console.log(alert);
                                    alerts.push({ type: 'global', message: alert, level: 'critical' });
                                }

                                resolve({
                                    changes: this.changes,
                                    newLocationStock,
                                    newGlobalStock,
                                    productName: row.description,
                                    alerts,
                                    success: true
                                });
                            });
                        });
                    });
                });
            });
        });
    }

    /**
     * Check reorder levels for all products at a location
     */
    async checkReorderLevels(locationName = null) {
        try {
            console.log('🔍 Checking reorder levels...');
            
            const query = `
                SELECT
                    p.id,
                    p.barcode,
                    p.description,
                    p.category,
                    p.subcategory,
                    p.supplier,
                    p.min_qty as product_min_qty,
                    p.max_qty,
                    ls.location,
                    COALESCE(ls.stock, 0) as stock,
                    COALESCE(p.min_qty, 0) as min_qty,
                    COALESCE(ls.price, 0) as price,
                    CASE
                        WHEN COALESCE(ls.stock, 0) <= COALESCE(p.min_qty, 0) THEN 'critical'
                        WHEN COALESCE(ls.stock, 0) <= (COALESCE(p.min_qty, 0) + ${this.lowStockThreshold}) THEN 'low'
                        ELSE 'normal'
                    END as stock_status
                FROM products p
                INNER JOIN location_stocks ls ON p.id = ls.product_id
                WHERE COALESCE(p.min_qty, 0) > 0
                ${locationName ? 'AND ls.location = ?' : ''}
                AND (COALESCE(ls.stock, 0) <= COALESCE(p.min_qty, 0) OR COALESCE(ls.stock, 0) <= (COALESCE(p.min_qty, 0) + ${this.lowStockThreshold}))
                ORDER BY
                    CASE
                        WHEN COALESCE(ls.stock, 0) <= COALESCE(p.min_qty, 0) THEN 1
                        ELSE 2
                    END,
                    COALESCE(ls.stock, 0) ASC
            `;
            
            const params = locationName ? [locationName] : [];
            
            return new Promise((resolve, reject) => {
                this.db.db.all(query, params, (err, rows) => {
                    if (err) {
                        console.error('❌ Error checking reorder levels:', err);
                        reject(err);
                    } else {
                        this.reorderAlerts = rows.map(row => ({
                            ...row,
                            reorder_quantity: Math.max(0, row.max_qty - row.stock),
                            days_until_stockout: null, // Will be calculated separately to avoid async issues
                            last_checked: new Date().toISOString()
                        }));

                        console.log(`📊 Found ${this.reorderAlerts.length} products needing reorder`);

                        // Log critical items for verification
                        const criticalItems = this.reorderAlerts.filter(item => item.stock_status === 'critical');
                        if (criticalItems.length > 0) {
                            console.log(`🚨 Critical items (stock <= min_qty):`, criticalItems.map(item =>
                                `${item.description} (Stock: ${item.stock}, Min: ${item.min_qty})`
                            ));
                        }
                        resolve(this.reorderAlerts);
                    }
                });
            });
            
        } catch (error) {
            console.error('❌ Error in checkReorderLevels:', error);
            throw error;
        }
    }

    /**
     * Get current reorder alerts
     */
    getReorderAlerts() {
        return this.reorderAlerts;
    }

    /**
     * Get detailed reorder report
     */
    async getReorderReport(locationName = null) {
        try {
            await this.checkReorderLevels(locationName);
            
            const report = {
                timestamp: new Date().toISOString(),
                location: locationName || 'All Locations',
                total_products_checked: await this.getTotalProductsCount(locationName),
                critical_stock: this.reorderAlerts.filter(item => item.stock_status === 'critical'),
                low_stock: this.reorderAlerts.filter(item => item.stock_status === 'low'),
                total_reorder_value: this.calculateTotalReorderValue(),
                alerts: this.reorderAlerts
            };
            
            return report;
            
        } catch (error) {
            console.error('❌ Error generating reorder report:', error);
            throw error;
        }
    }

    /**
     * Calculate estimated days until stockout based on sales velocity
     */
    async calculateDaysUntilStockout(currentStock, productId) {
        try {
            // Get average daily sales for the last 30 days
            const query = `
                SELECT AVG(daily_quantity) as avg_daily_sales
                FROM (
                    SELECT 
                        DATE(s.sale_date) as sale_date,
                        SUM(si.quantity) as daily_quantity
                    FROM sales_items si
                    JOIN sales s ON si.sale_id = s.sale_id
                    WHERE si.product_id = ?
                    AND s.sale_date >= date('now', '-30 days')
                    GROUP BY DATE(s.sale_date)
                )
            `;
            
            return new Promise((resolve) => {
                this.db.db.get(query, [productId], (err, row) => {
                    if (err || !row || !row.avg_daily_sales || row.avg_daily_sales <= 0) {
                        resolve(null); // Unknown velocity
                    } else {
                        const daysUntilStockout = Math.floor(currentStock / row.avg_daily_sales);
                        resolve(daysUntilStockout);
                    }
                });
            });
            
        } catch (error) {
            console.error('❌ Error calculating stockout days:', error);
            return null;
        }
    }

    /**
     * Get total products count for location
     */
    async getTotalProductsCount(locationName = null) {
        const query = locationName 
            ? `SELECT COUNT(*) as count FROM location_stocks WHERE location = ?`
            : `SELECT COUNT(*) as count FROM products`;
        
        const params = locationName ? [locationName] : [];
        
        return new Promise((resolve) => {
            this.db.db.get(query, params, (err, row) => {
                resolve(err ? 0 : (row?.count || 0));
            });
        });
    }

    /**
     * Calculate total value of items needing reorder
     */
    calculateTotalReorderValue() {
        return this.reorderAlerts.reduce((total, item) => {
            return total + (item.reorder_quantity * (item.price || 0));
        }, 0);
    }

    /**
     * Get stock status for a specific product
     */
    async getProductStockStatus(productId, locationName) {
        const query = `
            SELECT 
                p.description,
                p.min_qty,
                p.max_qty,
                ls.stock,
                ls.location,
                CASE 
                    WHEN ls.stock <= p.min_qty THEN 'critical'
                    WHEN ls.stock <= (p.min_qty + ?) THEN 'low'
                    ELSE 'normal'
                END as stock_status
            FROM products p
            LEFT JOIN location_stocks ls ON p.id = ls.product_id
            WHERE p.id = ? AND ls.location = ?
        `;
        
        return new Promise((resolve, reject) => {
            this.db.db.get(query, [this.lowStockThreshold, productId, locationName], (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    /**
     * Update minimum quantity for a product (updates product table only since location_stocks doesn't have min_qty)
     */
    async updateMinQuantity(productId, newMinQty) {
        const query = `
            UPDATE products
            SET min_qty = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `;

        return new Promise((resolve, reject) => {
            this.db.db.run(query, [newMinQty, productId], function(err) {
                if (err) {
                    reject(err);
                } else {
                    console.log(`📦 Updated min quantity for product ${productId} to ${newMinQty} in all location stocks`);
                    resolve({ changes: this.changes });
                }
            });
        });
    }

    /**
     * Add stock to inventory (for receiving new stock)
     */
    async addStock(productId, quantity, locationName, reason = 'stock_received') {
        const query = `
            UPDATE location_stocks 
            SET stock = stock + ?, updated_at = CURRENT_TIMESTAMP
            WHERE product_id = ? AND location = ?
        `;
        
        return new Promise((resolve, reject) => {
            this.db.db.run(query, [quantity, productId, locationName], function(err) {
                if (err) {
                    reject(err);
                } else {
                    console.log(`📦 Added ${quantity} stock for product ${productId} at ${locationName} (${reason})`);
                    resolve({ changes: this.changes });
                }
            });
        });
    }

    /**
     * Get inventory movement history
     */
    async getInventoryMovements(productId = null, locationName = null, days = 30) {
        const query = `
            SELECT 
                si.product_id,
                si.product_name,
                si.quantity,
                s.sale_date,
                s.location_name,
                'sale' as movement_type
            FROM sales_items si
            JOIN sales s ON si.sale_id = s.sale_id
            WHERE s.sale_date >= date('now', '-${days} days')
            ${productId ? 'AND si.product_id = ?' : ''}
            ${locationName ? 'AND s.location_name = ?' : ''}
            ORDER BY s.sale_date DESC
        `;
        
        const params = [];
        if (productId) params.push(productId);
        if (locationName) params.push(locationName);
        
        return new Promise((resolve, reject) => {
            this.db.db.all(query, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    /**
     * Set low stock threshold
     */
    setLowStockThreshold(threshold) {
        this.lowStockThreshold = threshold;
        console.log(`📦 Low stock threshold set to ${threshold}`);
    }
}

module.exports = InventoryService;
